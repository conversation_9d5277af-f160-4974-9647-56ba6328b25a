
import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export type Language = 'en' | 'af' | 'zu' | 'xh';

export const languageNames = {
  en: 'English',
  af: 'Afrikaans',
  zu: 'Zulu',
  xh: 'Xhosa',
};

export const languageFlags = {
  en: '🇺🇸',
  af: '🇿🇦',
  zu: '🇿🇦',
  xh: '🇿🇦',
};

interface LanguageContextType {
  currentLanguage: Language;
  setLanguage: (language: Language) => void;
  translate: (key: string, defaultValue?: string) => string;
  t: (key: string, defaultValue?: string) => string;
  availableLanguages: { code: Language; name: string; flag: string }[];
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

const availableLanguages: { code: Language; name: string; flag: string }[] = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'af', name: 'Afrikaans', flag: '🇿🇦' },
  { code: 'zu', name: 'Zulu', flag: '🇿🇦' },
  { code: 'xh', name: 'Xhosa', flag: '🇿🇦' },
];

// Basic translations for common UI elements
const translations: Record<string, Record<string, string>> = {
  en: {
    'welcome': 'Welcome',
    'search': 'Search',
    'categories': 'Categories',
    'featured_listings': 'Featured Listings',
    'view_all': 'View All',
    'contact': 'Contact',
    'phone': 'Phone',
    'website': 'Website',
    'address': 'Address',
    'hours': 'Hours',
    'reviews': 'Reviews',
    'rating': 'Rating',
    'add_review': 'Add Review',
    'submit': 'Submit',
    'cancel': 'Cancel',
    'save': 'Save',
    'edit': 'Edit',
    'delete': 'Delete',
    'loading': 'Loading...',
    'error': 'Error',
    'success': 'Success',
    'language.select': 'Select Language',
    'language.auto_translate': 'Auto-translation available',
  },
  af: {
    'welcome': 'Welkom',
    'search': 'Soek',
    'categories': 'Kategorieë',
    'featured_listings': 'Uitgestalde Lyste',
    'view_all': 'Sien Alles',
    'contact': 'Kontak',
    'phone': 'Telefoon',
    'website': 'Webwerf',
    'address': 'Adres',
    'hours': 'Ure',
    'reviews': 'Resensies',
    'rating': 'Gradering',
    'add_review': 'Voeg Resensie By',
    'submit': 'Stuur',
    'cancel': 'Kanselleer',
    'save': 'Stoor',
    'edit': 'Wysig',
    'delete': 'Vee Uit',
    'loading': 'Laai...',
    'error': 'Fout',
    'success': 'Sukses',
    'language.select': 'Kies Taal',
    'language.auto_translate': 'Auto-vertaling beskikbaar',
  },
  zu: {
    'welcome': 'Siyakwamukela',
    'search': 'Sesha',
    'categories': 'Izinhlobo',
    'featured_listings': 'Izinhla Ezibalulekile',
    'view_all': 'Bona Konke',
    'contact': 'Thintana',
    'phone': 'Ifoni',
    'website': 'Iwebhusayithi',
    'address': 'Ikheli',
    'hours': 'Amahora',
    'reviews': 'Ukubuyekeza',
    'rating': 'Isilinganiso',
    'add_review': 'Engeza Ukubuyekeza',
    'submit': 'Thumela',
    'cancel': 'Khansela',
    'save': 'Londoloza',
    'edit': 'Hlela',
    'delete': 'Susa',
    'loading': 'Iyalayisha...',
    'error': 'Iphutha',
    'success': 'Impumelelo',
    'language.select': 'Khetha Ulimi',
    'language.auto_translate': 'Ukuhumusha ngokuzenzakalelayo kuyatholakala',
  },
  xh: {
    'welcome': 'Wamkelekile',
    'search': 'Khangela',
    'categories': 'Iindidi',
    'featured_listings': 'Uluhlu Olubalulekileyo',
    'view_all': 'Bona Konke',
    'contact': 'Qhagamshelana',
    'phone': 'Ifowuni',
    'website': 'Iwebhusayithi',
    'address': 'Idilesi',
    'hours': 'Iiyure',
    'reviews': 'Uphononongo',
    'rating': 'Inqanaba',
    'add_review': 'Yongeza Uphononongo',
    'submit': 'Ngenisa',
    'cancel': 'Rhoxisa',
    'save': 'Gcina',
    'edit': 'Hlela',
    'delete': 'Cima',
    'loading': 'Iyalayisha...',
    'error': 'Impazamo',
    'success': 'Impumelelo',
    'language.select': 'Khetha Ulwimi',
    'language.auto_translate': 'Ukuguqulela ngokuzenzekelayo kuyafumaneka',
  },
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('en');

  useEffect(() => {
    // Load saved language preference from localStorage
    const savedLanguage = localStorage.getItem('preferred_language');
    if (savedLanguage && availableLanguages.some(lang => lang.code === savedLanguage)) {
      setCurrentLanguage(savedLanguage as Language);
    }
  }, []);

  const setLanguage = (language: Language) => {
    setCurrentLanguage(language);
    localStorage.setItem('preferred_language', language);
  };

  const translate = (key: string, defaultValue?: string) => {
    const translation = translations[currentLanguage]?.[key];
    return translation || defaultValue || key;
  };

  // Add t as an alias for translate
  const t = translate;

  const value = {
    currentLanguage,
    setLanguage,
    translate,
    t,
    availableLanguages,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
