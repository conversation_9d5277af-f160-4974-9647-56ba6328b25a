import React from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

const OfferManager = ({ businessId }: { businessId: string }) => {
  const queryClient = useQueryClient();
  
  const { data: offers = [] } = useQuery({
    queryKey: ['business-offers', businessId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('special_offers')
        .select('*')
        .eq('business_id', businessId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    }
  });

  const toggleOfferMutation = useMutation({
    mutationFn: async ({ offerId, isActive }: { offerId: string, isActive: boolean }) => {
      const { error } = await supabase
        .from('special_offers')
        .update({ is_active: !isActive })
        .eq('id', offerId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('Offer updated successfully');
      queryClient.invalidateQueries({ queryKey: ['business-offers', businessId] });
    }
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Your Special Offers</h2>
        <Button asChild>
          <Link to="/add-special-offer">Add New Offer</Link>
        </Button>
      </div>
      
      {offers.map((offer) => (
        <Card key={offer.id}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <CardTitle>{offer.title}</CardTitle>
              <div className="flex gap-2">
                <Badge variant={offer.status === 'approved' ? 'default' : 'secondary'}>
                  {offer.status}
                </Badge>
                <Badge variant={offer.is_active ? 'default' : 'destructive'}>
                  {offer.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">{offer.description}</p>
            <div className="flex justify-between items-center">
              <span className="text-sm">
                {offer.current_redemptions || 0} / {offer.max_redemptions || '∞'} used
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => toggleOfferMutation.mutate({ 
                  offerId: offer.id, 
                  isActive: offer.is_active 
                })}
              >
                {offer.is_active ? 'Deactivate' : 'Activate'}
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default OfferManager;