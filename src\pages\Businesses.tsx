
import React, { useState } from "react";
import Layout from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Search, MapPin, Filter, Grid3X3, List, SlidersHorizontal, TrendingUp, Star, Users } from "lucide-react";
import { useBusinessListings } from "@/hooks/useBusinessListings";
import BusinessCard from "@/components/business/BusinessCard";
import AdBanner from "@/components/ads/AdBanner";
import AffiliateLinks from "@/components/affiliate/AffiliateLinks";

const Businesses = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [location, setLocation] = useState("");
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'relevance' | 'rating' | 'newest'>('relevance');
  const [appliedFilters, setAppliedFilters] = useState<{
    searchTerm?: string;
    location?: string;
  }>({});

  const { data: businesses, isLoading, error } = useBusinessListings(appliedFilters);

  const handleSearch = () => {
    setAppliedFilters({
      searchTerm: searchTerm || undefined,
      location: location || undefined,
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <Layout>
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-[#007749] via-[#005a37] to-[#003087] text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Discover Amazing <span className="text-[#FDB913]">Businesses</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-slate-100">
              Connect with trusted local businesses across South Africa
            </p>

            {/* Enhanced Search Section */}
            <Card className="bg-white/95 backdrop-blur-sm shadow-2xl">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-4 top-3.5 h-5 w-5 text-slate-400" />
                    <Input
                      placeholder="What are you looking for?"
                      className="pl-12 h-12 text-lg border-slate-200 focus:border-[#007749] focus:ring-[#007749]"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      onKeyPress={handleKeyPress}
                    />
                  </div>
                  <div className="flex-1 relative">
                    <MapPin className="absolute left-4 top-3.5 h-5 w-5 text-slate-400" />
                    <Input
                      placeholder="Where?"
                      className="pl-12 h-12 text-lg border-slate-200 focus:border-[#007749] focus:ring-[#007749]"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      onKeyPress={handleKeyPress}
                    />
                  </div>
                  <Button
                    className="bg-[#007749] hover:bg-[#005a37] h-12 px-8 text-lg font-semibold shadow-lg"
                    onClick={handleSearch}
                  >
                    Search
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-slate-50 min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="flex gap-8">
            <div className="flex-1">
              {/* Top Ad Banner */}
              <AdBanner position="top" className="mb-8" />

              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
                  <CardContent className="p-6 text-center">
                    <div className="w-12 h-12 bg-[#007749] rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-slate-800 mb-2">
                      {businesses?.length || 0}
                    </h3>
                    <p className="text-slate-600">Active Businesses</p>
                  </CardContent>
                </Card>

                <Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
                  <CardContent className="p-6 text-center">
                    <div className="w-12 h-12 bg-[#FDB913] rounded-full flex items-center justify-center mx-auto mb-4">
                      <Star className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-slate-800 mb-2">4.8</h3>
                    <p className="text-slate-600">Average Rating</p>
                  </CardContent>
                </Card>

                <Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
                  <CardContent className="p-6 text-center">
                    <div className="w-12 h-12 bg-[#003087] rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-slate-800 mb-2">50K+</h3>
                    <p className="text-slate-600">Happy Customers</p>
                  </CardContent>
                </Card>
              </div>

              {/* Enhanced Results Header */}
              {!isLoading && businesses && (
                <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6 mb-8">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                    <div className="flex items-center gap-4">
                      <div>
                        <h2 className="text-xl font-semibold text-slate-800">
                          {businesses.length} Businesses Found
                        </h2>
                        <p className="text-slate-600">
                          {appliedFilters.searchTerm && `Results for "${appliedFilters.searchTerm}"`}
                          {appliedFilters.location && ` in ${appliedFilters.location}`}
                          {!appliedFilters.searchTerm && !appliedFilters.location && 'Showing all businesses'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      {/* Sort Dropdown */}
                      <div className="flex items-center gap-2">
                        <SlidersHorizontal className="h-4 w-4 text-slate-500" />
                        <select
                          value={sortBy}
                          onChange={(e) => setSortBy(e.target.value as any)}
                          className="border border-slate-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#007749] focus:border-transparent"
                        >
                          <option value="relevance">Most Relevant</option>
                          <option value="rating">Highest Rated</option>
                          <option value="newest">Newest First</option>
                        </select>
                      </div>

                      {/* View Toggle */}
                      <div className="flex items-center bg-slate-100 rounded-lg p-1">
                        <Button
                          variant={viewMode === 'grid' ? 'default' : 'ghost'}
                          size="sm"
                          onClick={() => setViewMode('grid')}
                          className={`h-8 w-8 p-0 ${viewMode === 'grid' ? 'bg-white shadow-sm' : ''}`}
                        >
                          <Grid3X3 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant={viewMode === 'list' ? 'default' : 'ghost'}
                          size="sm"
                          onClick={() => setViewMode('list')}
                          className={`h-8 w-8 p-0 ${viewMode === 'list' ? 'bg-white shadow-sm' : ''}`}
                        >
                          <List className="h-4 w-4" />
                        </Button>
                      </div>

                      <Button variant="outline" size="sm" className="flex items-center gap-2">
                        <Filter className="h-4 w-4" />
                        Filters
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Enhanced Loading state */}
              {isLoading && (
                <div className="space-y-8">
                  <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                    <div className="flex items-center justify-between">
                      <div className="h-6 bg-slate-200 rounded w-48 animate-pulse"></div>
                      <div className="h-10 bg-slate-200 rounded w-32 animate-pulse"></div>
                    </div>
                  </div>
                  <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
                    {[...Array(6)].map((_, i) => (
                      <Card key={i} className="overflow-hidden">
                        <div className="aspect-[16/9] bg-slate-200 animate-pulse"></div>
                        <CardContent className="p-4 space-y-3">
                          <div className="h-6 bg-slate-200 rounded animate-pulse"></div>
                          <div className="h-4 bg-slate-200 rounded w-3/4 animate-pulse"></div>
                          <div className="h-4 bg-slate-200 rounded w-1/2 animate-pulse"></div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* Enhanced Error state */}
              {error && (
                <Card className="bg-white shadow-sm">
                  <CardContent className="p-12 text-center">
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                      <Search className="h-8 w-8 text-red-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-slate-800 mb-4">
                      Oops! Something went wrong
                    </h3>
                    <p className="text-slate-600 mb-2">
                      We couldn't load the businesses right now.
                    </p>
                    <p className="text-sm text-slate-500 mb-8">
                      This might be because there are no approved business listings in the database yet.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                      <Button
                        onClick={() => window.location.reload()}
                        className="bg-[#007749] hover:bg-[#005a37]"
                      >
                        Try Again
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => window.location.href = '/test-seed'}
                      >
                        Add Sample Data
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Enhanced Businesses list */}
              {!isLoading && !error && businesses && (
                <>
                  {businesses.length > 0 ? (
                    <div className="space-y-8">
                      {/* Featured Businesses Section */}
                      {businesses.some(b => b.featured) && (
                        <div className="mb-12">
                          <div className="flex items-center gap-3 mb-6">
                            <div className="w-1 h-8 bg-[#FDB913] rounded-full"></div>
                            <h2 className="text-2xl font-bold text-slate-800">Featured Businesses</h2>
                            <Badge className="bg-[#FDB913] text-black">Premium</Badge>
                          </div>
                          <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
                            {businesses.filter(b => b.featured).slice(0, 3).map((business) => (
                              <BusinessCard
                                key={business.id}
                                business={business}
                                featured={true}
                              />
                            ))}
                          </div>
                        </div>
                      )}

                      {/* All Businesses Section */}
                      <div>
                        <div className="flex items-center gap-3 mb-6">
                          <div className="w-1 h-8 bg-[#007749] rounded-full"></div>
                          <h2 className="text-2xl font-bold text-slate-800">All Businesses</h2>
                        </div>
                        <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
                          {businesses.slice(0, 9).map((business) => (
                            <BusinessCard
                              key={business.id}
                              business={business}
                              featured={business.featured || false}
                            />
                          ))}
                        </div>
                      </div>

                      {/* Middle Ad Banner */}
                      {businesses.length > 9 && (
                        <AdBanner position="middle" className="my-8" />
                      )}

                      {/* Affiliate Links */}
                      <AffiliateLinks
                        context="business_listing"
                        category="business_services"
                        className="my-8"
                      />

                      {/* More Businesses */}
                      {businesses.length > 9 && (
                        <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
                          {businesses.slice(9).map((business) => (
                            <BusinessCard
                              key={business.id}
                              business={business}
                              featured={business.featured || false}
                            />
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Card className="bg-white shadow-sm">
                      <CardContent className="p-12 text-center">
                        <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-6">
                          <Search className="h-8 w-8 text-slate-400" />
                        </div>
                        <h3 className="text-xl font-semibold text-slate-800 mb-4">
                          No businesses found
                        </h3>
                        <p className="text-slate-600 mb-8">
                          {appliedFilters.searchTerm || appliedFilters.location
                            ? 'Try adjusting your search criteria or explore different locations.'
                            : 'Be the first to discover amazing businesses in your area!'
                          }
                        </p>
                        <div className="flex flex-col sm:flex-row gap-3 justify-center">
                          {(appliedFilters.searchTerm || appliedFilters.location) ? (
                            <Button
                              variant="outline"
                              onClick={() => {
                                setSearchTerm('');
                                setLocation('');
                                setAppliedFilters({});
                              }}
                              className="border-[#007749] text-[#007749] hover:bg-[#007749] hover:text-white"
                            >
                              Clear Filters
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              onClick={() => window.location.href = '/test-seed'}
                              className="border-[#007749] text-[#007749] hover:bg-[#007749] hover:text-white"
                            >
                              Add Sample Data
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </>
              )}

              {/* Bottom Ad Banner */}
              <AdBanner position="bottom" className="my-8" />

              {/* Enhanced Pagination */}
              {!isLoading && businesses && businesses.length > 0 && (
                <Card className="bg-white shadow-sm mt-12">
                  <CardContent className="p-6">
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                      <p className="text-slate-600">
                        Showing 1-{Math.min(businesses.length, 12)} of {businesses.length} results
                      </p>
                      <nav className="flex items-center gap-2">
                        <Button variant="outline" size="sm" disabled className="text-slate-400">
                          Previous
                        </Button>
                        <Button size="sm" className="bg-[#007749] text-white">
                          1
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-[#007749] hover:text-white">
                          2
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-[#007749] hover:text-white">
                          3
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-[#007749] hover:text-white">
                          Next
                        </Button>
                      </nav>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Enhanced Sidebar */}
            <div className="hidden lg:block w-80">
              <div className="sticky top-4 space-y-6">
                {/* Quick Filters */}
                <Card className="bg-white shadow-sm">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-slate-800 mb-4">Quick Filters</h3>
                    <div className="space-y-3">
                      <Button variant="outline" className="w-full justify-start text-left">
                        <Star className="h-4 w-4 mr-2" />
                        Top Rated (4.5+)
                      </Button>
                      <Button variant="outline" className="w-full justify-start text-left">
                        <Badge className="h-4 w-4 mr-2" />
                        Verified Only
                      </Button>
                      <Button variant="outline" className="w-full justify-start text-left">
                        <TrendingUp className="h-4 w-4 mr-2" />
                        Recently Added
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <AdBanner position="sidebar" />
                <AffiliateLinks
                  context="business_listing"
                  category="business_services"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Businesses;
