
import React, { useState } from "react";
import Layout from "@/components/layout/Layout";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Heart, MessageSquareHeart, Share2 } from "lucide-react";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

type Story = {
  id: string;
  businessName: string;
  title: string;
  excerpt: string;
  thumbnail: string;
  category: string;
  isPremium: boolean;
  date: string;
  type: "video" | "article" | "podcast";
  views: number;
  likes: number;
};

const BusinessStories = () => {
  const { data: stories = [], isLoading } = useQuery({
    queryKey: ['business-stories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('business_stories')
        .select('*')
        .eq('status', 'approved')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    }
  });

  const [activeTab, setActiveTab] = useState("all");
  const [likedStories, setLikedStories] = useState<Record<string, boolean>>({});

  const handleLike = (storyId: string) => {
    setLikedStories(prev => ({
      ...prev,
      [storyId]: !prev[storyId]
    }));
  };

  const filteredStories = activeTab === "all" 
    ? stories 
    : stories.filter(story => story.type === activeTab);

  return (
    <Layout>
      <div className="py-10">
        <div className="container mx-auto px-4">
          <div className="mb-8 text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
              South African Business Stories
            </h1>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              Discover the inspiring journeys, challenges, and triumphs behind 
              South Africa's most notable businesses.
            </p>
          </div>

          <Tabs defaultValue="all" className="mb-8" onValueChange={setActiveTab}>
            <div className="flex justify-center mb-6">
              <TabsList>
                <TabsTrigger value="all">All Stories</TabsTrigger>
                <TabsTrigger value="video">Videos</TabsTrigger>
                <TabsTrigger value="article">Articles</TabsTrigger>
                <TabsTrigger value="podcast">Podcasts</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredStories.map((story) => (
                  <StoryCard 
                    key={story.id} 
                    story={story}
                    isLiked={!!likedStories[story.id]}
                    onLike={handleLike}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="video" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredStories.map((story) => (
                  <StoryCard 
                    key={story.id} 
                    story={story}
                    isLiked={!!likedStories[story.id]}
                    onLike={handleLike}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="article" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredStories.map((story) => (
                  <StoryCard 
                    key={story.id} 
                    story={story}
                    isLiked={!!likedStories[story.id]}
                    onLike={handleLike}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="podcast" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredStories.map((story) => (
                  <StoryCard 
                    key={story.id} 
                    story={story}
                    isLiked={!!likedStories[story.id]}
                    onLike={handleLike}
                  />
                ))}
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="text-center mt-10">
            <Button className="bg-[#007749] hover:bg-[#006739]">
              Load More Stories
            </Button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

interface StoryCardProps {
  story: Story;
  isLiked: boolean;
  onLike: (storyId: string) => void;
}

const StoryCard = ({ story, isLiked, onLike }: StoryCardProps) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "video":
        return <div className="absolute top-2 right-2 bg-red-600 p-1 rounded-full"><MessageSquareHeart className="h-4 w-4 text-white" /></div>;
      case "podcast":
        return <div className="absolute top-2 right-2 bg-purple-600 p-1 rounded-full"><MessageSquareHeart className="h-4 w-4 text-white" /></div>;
      default:
        return <div className="absolute top-2 right-2 bg-blue-600 p-1 rounded-full"><MessageSquareHeart className="h-4 w-4 text-white" /></div>;
    }
  };

  return (
    <Card className="overflow-hidden h-full flex flex-col">
      <div className="relative">
        <Link to={`/stories/${story.id}`}>
          <div className="aspect-[16/9] overflow-hidden">
            <img 
              src={story.thumbnail} 
              alt={story.title} 
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            />
          </div>
        </Link>
        {getTypeIcon(story.type)}
        {story.isPremium && (
          <Badge className="absolute top-2 left-2 bg-[#FDB913] text-black">Premium</Badge>
        )}
      </div>
      
      <CardContent className="flex-1 flex flex-col p-5">
        <div className="mb-2">
          <span className="text-sm text-[#007749] font-medium">{story.category}</span>
        </div>
        
        <Link to={`/stories/${story.id}`} className="group">
          <h3 className="font-semibold text-lg text-slate-800 mb-2 group-hover:text-[#007749] transition-colors">
            {story.title}
          </h3>
        </Link>
        
        <p className="text-slate-600 text-sm mb-4 flex-1">{story.excerpt}</p>
        
        <div className="mt-auto">
          <div className="flex justify-between items-center">
            <span className="text-sm text-slate-500">{story.date}</span>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => onLike(story.id)}
                className={`flex items-center text-sm ${isLiked ? 'text-red-500' : 'text-slate-500'}`}
              >
                <Heart className={`h-4 w-4 mr-1 ${isLiked ? 'fill-current' : ''}`} />
                {story.likes + (isLiked ? 1 : 0)}
              </button>
              <button className="flex items-center text-sm text-slate-500">
                <Share2 className="h-4 w-4 mr-1" />
                Share
              </button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BusinessStories;
