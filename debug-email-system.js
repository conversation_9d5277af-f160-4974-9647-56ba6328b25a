/**
 * Debug script for the listing approval email system
 * This script helps identify where the email notification system is failing
 * 
 * Run with: node debug-email-system.js
 * 
 * Prerequisites:
 * 1. npm install @supabase/supabase-js
 * 2. Update SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY below
 */

import { createClient } from '@supabase/supabase-js';

// Configuration - Update these with your actual values
const SUPABASE_URL = 'https://tcqltnxugtzludqhngrs.supabase.co'; // Your actual Supabase URL
const SUPABASE_SERVICE_ROLE_KEY = 'your-service-role-key-here'; // Replace with your actual service role key

// NOTE: You can find your service role key in:
// Supabase Dashboard → Settings → API → service_role key

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function debugEmailSystem() {
  console.log('🔍 Debugging Email Notification System\n');

  try {
    // Step 1: Check if tables exist
    console.log('1. Checking if required tables exist...');
    await checkTablesExist();

    // Step 2: Check if triggers exist
    console.log('\n2. Checking database triggers...');
    await checkTriggers();

    // Step 3: Check recent listings
    console.log('\n3. Checking recent listings...');
    await checkRecentListings();

    // Step 4: Check email notifications queue
    console.log('\n4. Checking email notifications queue...');
    await checkEmailNotifications();

    // Step 5: Check edge functions
    console.log('\n5. Checking edge functions...');
    await checkEdgeFunctions();

    // Step 6: Test email processing
    console.log('\n6. Testing email processing...');
    await testEmailProcessing();

    // Step 7: Check environment variables
    console.log('\n7. Checking environment setup...');
    await checkEnvironment();

    console.log('\n✅ Debug complete! Check the output above for any issues.');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error('Full error:', error);
  }
}

async function checkTablesExist() {
  const tables = ['business_listings', 'email_notifications', 'listing_approval_tokens'];
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ Table '${table}' error:`, error.message);
      } else {
        console.log(`✅ Table '${table}' exists`);
      }
    } catch (error) {
      console.log(`❌ Table '${table}' not accessible:`, error.message);
    }
  }
}

async function checkTriggers() {
  try {
    // Check if the trigger function exists
    const { data: functions, error: funcError } = await supabase
      .rpc('sql', {
        query: `
          SELECT proname, prosrc 
          FROM pg_proc 
          WHERE proname = 'send_listing_approval_email';
        `
      });

    if (funcError) {
      console.log('❌ Cannot check trigger function:', funcError.message);
    } else if (functions && functions.length > 0) {
      console.log('✅ Trigger function exists');
    } else {
      console.log('❌ Trigger function does not exist');
    }

    // Check if the trigger exists
    const { data: triggers, error: trigError } = await supabase
      .rpc('sql', {
        query: `
          SELECT tgname, tgenabled 
          FROM pg_trigger 
          WHERE tgname = 'trigger_send_listing_approval_email';
        `
      });

    if (trigError) {
      console.log('❌ Cannot check trigger:', trigError.message);
    } else if (triggers && triggers.length > 0) {
      console.log('✅ Trigger exists and is', triggers[0].tgenabled === 'O' ? 'enabled' : 'disabled');
    } else {
      console.log('❌ Trigger does not exist');
    }

  } catch (error) {
    console.log('❌ Error checking triggers:', error.message);
    console.log('ℹ️  This might be due to RPC permissions. Try checking manually in the database.');
  }
}

async function checkRecentListings() {
  try {
    const { data: listings, error } = await supabase
      .from('business_listings')
      .select('id, title, status, created_at')
      .order('created_at', { ascending: false })
      .limit(5);

    if (error) {
      console.log('❌ Error fetching listings:', error.message);
      return;
    }

    if (!listings || listings.length === 0) {
      console.log('⚠️  No listings found');
      return;
    }

    console.log('Recent listings:');
    listings.forEach((listing, index) => {
      console.log(`  ${index + 1}. ${listing.title} (${listing.status}) - ${new Date(listing.created_at).toLocaleString()}`);
    });

    // Check for pending listings specifically
    const { data: pendingListings, error: pendingError } = await supabase
      .from('business_listings')
      .select('id, title, created_at')
      .eq('status', 'pending')
      .order('created_at', { ascending: false })
      .limit(3);

    if (pendingError) {
      console.log('❌ Error fetching pending listings:', pendingError.message);
    } else if (pendingListings && pendingListings.length > 0) {
      console.log(`\n✅ Found ${pendingListings.length} pending listings:`);
      pendingListings.forEach((listing, index) => {
        console.log(`  ${index + 1}. ${listing.title} - ${new Date(listing.created_at).toLocaleString()}`);
      });
    } else {
      console.log('\n⚠️  No pending listings found');
    }

  } catch (error) {
    console.log('❌ Error checking listings:', error.message);
  }
}

async function checkEmailNotifications() {
  try {
    const { data: notifications, error } = await supabase
      .from('email_notifications')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) {
      console.log('❌ Error fetching email notifications:', error.message);
      return;
    }

    if (!notifications || notifications.length === 0) {
      console.log('⚠️  No email notifications found in queue');
      console.log('   This suggests the database trigger is not firing');
      return;
    }

    console.log(`Found ${notifications.length} email notifications:`);
    notifications.forEach((notification, index) => {
      console.log(`  ${index + 1}. ${notification.type} - ${notification.status} (${notification.attempts} attempts)`);
      console.log(`     To: ${notification.recipient_email}`);
      console.log(`     Subject: ${notification.subject}`);
      console.log(`     Created: ${new Date(notification.created_at).toLocaleString()}`);
      if (notification.error_message) {
        console.log(`     Error: ${notification.error_message}`);
      }
      console.log('');
    });

    // Check status distribution
    const { data: statusCounts, error: statusError } = await supabase
      .from('email_notifications')
      .select('status')
      .eq('type', 'listing_approval');

    if (!statusError && statusCounts) {
      const counts = statusCounts.reduce((acc, item) => {
        acc[item.status] = (acc[item.status] || 0) + 1;
        return acc;
      }, {});
      console.log('Status distribution:', counts);
    }

  } catch (error) {
    console.log('❌ Error checking email notifications:', error.message);
  }
}

async function checkEdgeFunctions() {
  const functions = [
    'send-listing-approval-email',
    'approve-listing', 
    'process-email-notifications'
  ];

  for (const funcName of functions) {
    try {
      console.log(`Testing ${funcName}...`);
      
      // Try to invoke the function with minimal data
      const { data, error } = await supabase.functions.invoke(funcName, {
        body: funcName === 'process-email-notifications' ? {} : { test: true }
      });

      if (error) {
        if (error.message.includes('not found') || error.message.includes('404')) {
          console.log(`❌ Function '${funcName}' not deployed`);
        } else {
          console.log(`⚠️  Function '${funcName}' exists but returned error:`, error.message);
        }
      } else {
        console.log(`✅ Function '${funcName}' is accessible`);
      }
    } catch (error) {
      console.log(`❌ Function '${funcName}' error:`, error.message);
    }
  }
}

async function testEmailProcessing() {
  try {
    console.log('Attempting to process email notifications...');
    
    const { data, error } = await supabase.functions.invoke('process-email-notifications');

    if (error) {
      console.log('❌ Email processing failed:', error.message);
      return;
    }

    console.log('✅ Email processing response:', data);
    
    if (data && data.processed !== undefined) {
      console.log(`   Processed: ${data.processed} emails`);
      console.log(`   Failed: ${data.failed || 0} emails`);
    }

  } catch (error) {
    console.log('❌ Error testing email processing:', error.message);
  }
}

async function checkEnvironment() {
  console.log('Environment check:');
  console.log(`  Supabase URL: ${SUPABASE_URL}`);
  console.log(`  Service Role Key: ${SUPABASE_SERVICE_ROLE_KEY ? 'Set (length: ' + SUPABASE_SERVICE_ROLE_KEY.length + ')' : 'Not set'}`);
  
  if (SUPABASE_SERVICE_ROLE_KEY === 'your-service-role-key-here') {
    console.log('❌ Please update the SUPABASE_SERVICE_ROLE_KEY in this script');
  }

  // Try to check if Resend API key is configured (this will only work if the function runs)
  console.log('\nTo check Resend API key configuration:');
  console.log('1. Go to your Supabase dashboard');
  console.log('2. Navigate to Settings > Environment Variables');
  console.log('3. Ensure RESEND_API_KEY is set');
  console.log('4. Verify your domain is verified in Resend');
}

// Helper function to create a test listing for debugging
async function createTestListing() {
  console.log('\n🧪 Creating test listing...');
  
  try {
    // First, get a user ID (create one if needed)
    const { data: users, error: userError } = await supabase.auth.admin.listUsers();
    
    let userId;
    if (userError || !users.users.length) {
      console.log('Creating test user...');
      const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'testpassword123',
        email_confirm: true
      });
      
      if (createError) {
        throw new Error(`Failed to create user: ${createError.message}`);
      }
      userId = newUser.user.id;
    } else {
      userId = users.users[0].id;
    }

    // Create test listing
    const { data: listing, error } = await supabase
      .from('business_listings')
      .insert({
        user_id: userId,
        title: `Debug Test Listing ${Date.now()}`,
        description: 'This is a test listing for debugging the email system',
        city: 'Cape Town',
        phone: '+***********',
        status: 'pending'
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create test listing: ${error.message}`);
    }

    console.log('✅ Test listing created:', listing.title);
    console.log('   ID:', listing.id);
    console.log('   Status:', listing.status);
    
    return listing;

  } catch (error) {
    console.log('❌ Failed to create test listing:', error.message);
    return null;
  }
}

// Add option to create test listing
if (process.argv.includes('--create-test')) {
  createTestListing().then(() => {
    console.log('\nTest listing created. Run the debug script again to check if email was queued.');
  });
} else {
  debugEmailSystem();
}
