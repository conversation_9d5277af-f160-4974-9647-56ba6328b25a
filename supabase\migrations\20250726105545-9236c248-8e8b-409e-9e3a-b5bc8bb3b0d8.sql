
-- Add missing columns to business_reviews table
ALTER TABLE public.business_reviews 
ADD COLUMN IF NOT EXISTS helpful_votes INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_votes INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS response_from_business TEXT,
ADD COLUMN IF NOT EXISTS response_date TIMESTAMP WITH TIME ZONE;

-- Create review_votes table for tracking review helpfulness
CREATE TABLE IF NOT EXISTS public.review_votes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  review_id UUID NOT NULL REFERENCES business_reviews(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  vote_type TEXT NOT NULL CHECK (vote_type IN ('helpful', 'not_helpful')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(review_id, user_id)
);

-- Create business_reputation table for aggregate review data
CREATE TABLE IF NOT EXISTS public.business_reputation (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id UUID NOT NULL REFERENCES business_listings(id) ON DELETE CASCADE UNIQUE,
  average_rating DECIMAL DEFAULT 0,
  total_reviews INTEGER DEFAULT 0,
  verified_reviews INTEGER DEFAULT 0,
  recommended_percentage DECIMAL DEFAULT 0,
  response_rate DECIMAL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add RLS policies for review_votes
ALTER TABLE public.review_votes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view review votes" 
  ON public.review_votes 
  FOR SELECT 
  USING (true);

CREATE POLICY "Users can create vote on reviews" 
  ON public.review_votes 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own votes" 
  ON public.review_votes 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Add RLS policies for business_reputation
ALTER TABLE public.business_reputation ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view business reputation" 
  ON public.business_reputation 
  FOR SELECT 
  USING (true);

CREATE POLICY "System can manage reputation data" 
  ON public.business_reputation 
  FOR ALL 
  USING (true);

-- Add triggers for updated_at
CREATE TRIGGER IF NOT EXISTS update_business_reputation_updated_at 
  BEFORE UPDATE ON public.business_reputation 
  FOR EACH ROW 
  EXECUTE FUNCTION public.update_updated_at_column();
