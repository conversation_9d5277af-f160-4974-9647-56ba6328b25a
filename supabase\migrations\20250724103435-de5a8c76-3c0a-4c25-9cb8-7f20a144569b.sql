
-- Create events table
CREATE TABLE public.events (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  event_type TEXT NOT NULL,
  category TEXT,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE,
  location TEXT NOT NULL,
  venue_name TEXT,
  city TEXT NOT NULL,
  organizer_name TEXT,
  website_url TEXT,
  ticket_url TEXT,
  price_range TEXT,
  images TEXT[],
  tags TEXT[],
  featured BOOLEAN DEFAULT false,
  status TEXT NOT NULL DEFAULT 'published',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create business_event_connections table
CREATE TABLE public.business_event_connections (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id UUID NOT NULL REFERENCES business_listings(id) ON DELETE CASCADE,
  event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  connection_type TEXT NOT NULL,
  description TEXT,
  special_offer TEXT,
  discount_code TEXT,
  is_featured BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create event_interests table
CREATE TABLE public.event_interests (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  interest_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(event_id, user_id)
);

-- Add RLS policies for events table
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Events are publicly readable" 
  ON public.events 
  FOR SELECT 
  USING (status = 'published');

CREATE POLICY "Authenticated users can create events" 
  ON public.events 
  FOR INSERT 
  WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update events" 
  ON public.events 
  FOR UPDATE 
  USING (auth.role() = 'authenticated');

-- Add RLS policies for business_event_connections table
ALTER TABLE public.business_event_connections ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Business owners can manage their event connections" 
  ON public.business_event_connections 
  FOR ALL 
  USING (EXISTS (
    SELECT 1 FROM business_listings bl 
    WHERE bl.id = business_event_connections.business_id 
    AND bl.user_id = auth.uid()
  ));

CREATE POLICY "Anyone can view event connections" 
  ON public.business_event_connections 
  FOR SELECT 
  USING (true);

-- Add RLS policies for event_interests table
ALTER TABLE public.event_interests ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own event interests" 
  ON public.event_interests 
  FOR ALL 
  USING (auth.uid() = user_id);

-- Add triggers for updated_at
CREATE TRIGGER update_events_updated_at 
  BEFORE UPDATE ON public.events 
  FOR EACH ROW 
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_business_event_connections_updated_at 
  BEFORE UPDATE ON public.business_event_connections 
  FOR EACH ROW 
  EXECUTE FUNCTION public.update_updated_at_column();

-- Insert some sample events
INSERT INTO public.events (title, description, event_type, category, start_date, end_date, location, venue_name, city, organizer_name, website_url, price_range, featured, tags) VALUES
('Cape Town Tech Summit 2024', 'Join industry leaders for a day of innovation and networking', 'conference', 'technology', '2024-03-15 09:00:00+02', '2024-03-15 17:00:00+02', '123 Tech Street, Cape Town', 'Cape Town Convention Centre', 'Cape Town', 'Tech Events SA', 'https://techsummit.co.za', 'R500 - R1500', true, ARRAY['technology', 'networking', 'innovation']),
('Johannesburg Music Festival', 'Three days of amazing music and entertainment', 'festival', 'music', '2024-04-20 18:00:00+02', '2024-04-22 23:00:00+02', '456 Music Ave, Johannesburg', 'Johannesburg Stadium', 'Johannesburg', 'Music Events JHB', 'https://musicfest.co.za', 'R200 - R800', true, ARRAY['music', 'festival', 'entertainment']),
('Durban Business Workshop', 'Learn essential business skills from experts', 'workshop', 'business', '2024-05-10 10:00:00+02', '2024-05-10 16:00:00+02', '789 Business Blvd, Durban', 'Durban Business Centre', 'Durban', 'Business Skills SA', 'https://businessworkshop.co.za', 'R300 - R600', false, ARRAY['business', 'workshop', 'skills']),
('Pretoria Sports Day', 'Community sports event for all ages', 'sports', 'community', '2024-06-05 08:00:00+02', '2024-06-05 18:00:00+02', '321 Sports Way, Pretoria', 'Pretoria Sports Complex', 'Pretoria', 'Pretoria Sports Club', 'https://sportsevent.co.za', 'Free', false, ARRAY['sports', 'community', 'family']),
('Port Elizabeth Art Exhibition', 'Showcase of local and international artists', 'exhibition', 'arts', '2024-07-12 10:00:00+02', '2024-07-25 18:00:00+02', '654 Art Street, Port Elizabeth', 'Port Elizabeth Art Gallery', 'Port Elizabeth', 'PE Arts Council', 'https://artexhibition.co.za', 'R50 - R150', false, ARRAY['arts', 'exhibition', 'culture']);
