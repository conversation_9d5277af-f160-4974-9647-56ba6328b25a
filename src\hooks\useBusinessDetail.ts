import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { BusinessListing } from './useBusinessListings';

export const useBusinessDetail = (businessId: string) => {
  return useQuery({
    queryKey: ['business-detail', businessId],
    queryFn: async () => {
      console.log('Fetching business detail for ID:', businessId);

      try {
        const { data, error } = await supabase
          .from('business_listings')
          .select(`
            *,
            categories (
              name,
              slug
            )
          `)
          .eq('id', businessId)
          .eq('status', 'approved')
          .single();

        if (error) {
          console.error('Supabase error details:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code
          });
          throw error;
        }

        console.log('Fetched business detail:', data);
        return data as BusinessListing;
      } catch (error) {
        console.error('Unexpected error in useBusinessDetail:', error);
        throw error;
      }
    },
    retry: 1,
    retryDelay: 1000,
    enabled: !!businessId,
  });
};
