-- Quick fix script for email notification system
-- Run this in your Supabase SQL editor if you're having issues

-- 1. Ensure the email_notifications table exists with correct structure
CREATE TABLE IF NOT EXISTS public.email_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type TEXT NOT NULL,
  recipient_email TEXT NOT NULL,
  subject TEXT NOT NULL,
  data JSONB,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed')),
  attempts INTEGER DEFAULT 0,
  last_attempt_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Ensure the listing_approval_tokens table exists
CREATE TABLE IF NOT EXISTS public.listing_approval_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  listing_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE,
  token TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_email_notifications_status ON public.email_notifications(status);
CREATE INDEX IF NOT EXISTS idx_email_notifications_type ON public.email_notifications(type);
CREATE INDEX IF NOT EXISTS idx_email_notifications_created_at ON public.email_notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_listing_approval_tokens_token ON public.listing_approval_tokens(token);
CREATE INDEX IF NOT EXISTS idx_listing_approval_tokens_listing_id ON public.listing_approval_tokens(listing_id);

-- 4. Recreate the trigger function with proper error handling
CREATE OR REPLACE FUNCTION public.send_listing_approval_email()
RETURNS TRIGGER AS $$
BEGIN
  -- Only send email for new pending listings
  IF NEW.status = 'pending' AND (OLD IS NULL OR OLD.status != 'pending') THEN
    BEGIN
      -- Insert a notification record that will be processed by the application
      INSERT INTO public.email_notifications (
        type,
        recipient_email,
        subject,
        data,
        status
      ) VALUES (
        'listing_approval',
        '<EMAIL>',
        'New Listing Approval Required: ' || NEW.title,
        jsonb_build_object(
          'listingId', NEW.id,
          'listingTitle', NEW.title,
          'submittedAt', NEW.created_at
        ),
        'pending'
      );
      
      -- Log success (this will appear in Supabase logs)
      RAISE NOTICE 'Email notification queued for listing: %', NEW.title;
      
    EXCEPTION WHEN OTHERS THEN
      -- Log the error but don't fail the listing insertion
      RAISE WARNING 'Failed to queue email notification for listing %: %', NEW.id, SQLERRM;
    END;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Drop and recreate the trigger to ensure it's working
DROP TRIGGER IF EXISTS trigger_send_listing_approval_email ON public.business_listings;
CREATE TRIGGER trigger_send_listing_approval_email
  AFTER INSERT OR UPDATE ON public.business_listings
  FOR EACH ROW
  EXECUTE FUNCTION public.send_listing_approval_email();

-- 6. Fix RLS policies
ALTER TABLE public.email_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.listing_approval_tokens ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Service role can manage email notifications" ON public.email_notifications;
DROP POLICY IF EXISTS "Allow authenticated to insert email notifications" ON public.email_notifications;
DROP POLICY IF EXISTS "Service role can manage approval tokens" ON public.listing_approval_tokens;

-- Create new policies that work with the trigger
CREATE POLICY "Service role can manage email notifications" ON public.email_notifications
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Allow trigger to insert email notifications" ON public.email_notifications
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Service role can manage approval tokens" ON public.listing_approval_tokens
  FOR ALL USING (auth.role() = 'service_role');

-- 7. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.email_notifications TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.listing_approval_tokens TO authenticated;

-- 8. Test the trigger by creating a test listing
-- UNCOMMENT THE FOLLOWING LINES TO CREATE A TEST LISTING
/*
DO $$
DECLARE
  test_user_id UUID;
  test_listing_id UUID;
BEGIN
  -- Get or create a test user
  SELECT id INTO test_user_id FROM auth.users LIMIT 1;
  
  IF test_user_id IS NULL THEN
    RAISE EXCEPTION 'No users found. Please create a user first.';
  END IF;
  
  -- Create test listing
  INSERT INTO public.business_listings (
    user_id,
    title,
    description,
    city,
    status
  ) VALUES (
    test_user_id,
    'Test Listing for Email System ' || NOW(),
    'This is a test listing to verify the email notification system is working',
    'Test City',
    'pending'
  ) RETURNING id INTO test_listing_id;
  
  RAISE NOTICE 'Test listing created with ID: %', test_listing_id;
  
  -- Check if email notification was created
  IF EXISTS (
    SELECT 1 FROM public.email_notifications 
    WHERE data->>'listingId' = test_listing_id::text
  ) THEN
    RAISE NOTICE 'SUCCESS: Email notification was created for test listing';
  ELSE
    RAISE WARNING 'FAILED: No email notification was created for test listing';
  END IF;
END $$;
*/

-- 9. Check the results
SELECT 'Email notifications count:' as info, COUNT(*)::text as value FROM public.email_notifications
UNION ALL
SELECT 'Pending notifications:' as info, COUNT(*)::text as value FROM public.email_notifications WHERE status = 'pending'
UNION ALL
SELECT 'Failed notifications:' as info, COUNT(*)::text as value FROM public.email_notifications WHERE status = 'failed'
UNION ALL
SELECT 'Trigger exists:' as info, 
  CASE WHEN EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_send_listing_approval_email'
  ) THEN 'YES' ELSE 'NO' END as value
UNION ALL
SELECT 'Function exists:' as info,
  CASE WHEN EXISTS (
    SELECT 1 FROM pg_proc WHERE proname = 'send_listing_approval_email'
  ) THEN 'YES' ELSE 'NO' END as value;

-- 10. Show recent email notifications
SELECT 
  'Recent email notifications:' as section,
  id,
  type,
  recipient_email,
  subject,
  status,
  attempts,
  error_message,
  created_at
FROM public.email_notifications 
ORDER BY created_at DESC 
LIMIT 5;
