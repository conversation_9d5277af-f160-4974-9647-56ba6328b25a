import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Globe, 
  Languages, 
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface BusinessTranslation {
  id: string;
  business_id: string;
  language_code: string;
  title: string | null;
  description: string | null;
  translated_by: string;
  translation_quality: number;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

interface BusinessListing {
  id: string;
  title: string;
  description: string | null;
}

const SUPPORTED_LANGUAGES = [
  { code: 'af', name: 'Afrikaans' },
  { code: 'zu', name: 'Zulu' },
  { code: 'xh', name: 'Xhosa' },
  { code: 'st', name: 'Sotho' },
  { code: 'tn', name: 'Tswana' },
  { code: 've', name: 'Venda' },
  { code: 'ts', name: 'Tsonga' },
  { code: 'ss', name: 'Swati' },
  { code: 'nd', name: 'Ndebele' },
  { code: 'nr', name: 'Ndebele (South)' },
  { code: 'nso', name: 'Northern Sotho' },
];

const TranslationManager = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [selectedBusinessId, setSelectedBusinessId] = useState<string>('');
  const [selectedLanguage, setSelectedLanguage] = useState<string>('');
  const [translatedTitle, setTranslatedTitle] = useState<string>('');
  const [translatedDescription, setTranslatedDescription] = useState<string>('');
  const [editingTranslation, setEditingTranslation] = useState<BusinessTranslation | null>(null);

  const { data: userBusinesses } = useQuery({
    queryKey: ['user-businesses', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      const { data, error } = await supabase
        .from('business_listings')
        .select('id, title, description')
        .eq('user_id', user.id)
        .eq('status', 'approved');

      if (error) throw error;
      return (data || []) as BusinessListing[];
    },
    enabled: !!user?.id,
  });

  const { data: translations, refetch: refetchTranslations } = useQuery({
    queryKey: ['business-translations', selectedBusinessId],
    queryFn: async () => {
      if (!selectedBusinessId) return [];
      
      try {
        const { data, error } = await supabase
          .from('business_listing_translations')
          .select('*')
          .eq('business_id', selectedBusinessId)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching translations:', error);
          return [];
        }
        return (data || []) as BusinessTranslation[];
      } catch (error) {
        console.error('Error in translations query:', error);
        return [];
      }
    },
    enabled: !!selectedBusinessId,
  });

  const createTranslationMutation = useMutation({
    mutationFn: async (translationData: {
      business_id: string;
      language_code: string;
      title: string;
      description: string;
      translated_by: string;
    }) => {
      const { error } = await supabase
        .from('business_listing_translations')
        .insert(translationData);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('Translation created successfully!');
      setTranslatedTitle('');
      setTranslatedDescription('');
      setSelectedLanguage('');
      refetchTranslations();
    },
    onError: (error) => {
      console.error('Error creating translation:', error);
      toast.error('Failed to create translation');
    },
  });

  const updateTranslationMutation = useMutation({
    mutationFn: async (data: {
      id: string;
      title: string;
      description: string;
    }) => {
      const { error } = await supabase
        .from('business_listing_translations')
        .update({
          title: data.title,
          description: data.description,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('Translation updated successfully!');
      setEditingTranslation(null);
      setTranslatedTitle('');
      setTranslatedDescription('');
      refetchTranslations();
    },
    onError: (error) => {
      console.error('Error updating translation:', error);
      toast.error('Failed to update translation');
    },
  });

  const deleteTranslationMutation = useMutation({
    mutationFn: async (translationId: string) => {
      const { error } = await supabase
        .from('business_listing_translations')
        .delete()
        .eq('id', translationId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('Translation deleted successfully!');
      refetchTranslations();
    },
    onError: (error) => {
      console.error('Error deleting translation:', error);
      toast.error('Failed to delete translation');
    },
  });

  const handleCreateTranslation = () => {
    if (!selectedBusinessId || !selectedLanguage || !translatedTitle) {
      toast.error('Please fill in all required fields');
      return;
    }

    createTranslationMutation.mutate({
      business_id: selectedBusinessId,
      language_code: selectedLanguage,
      title: translatedTitle,
      description: translatedDescription,
      translated_by: 'user'
    });
  };

  const handleUpdateTranslation = () => {
    if (!editingTranslation || !translatedTitle) {
      toast.error('Please fill in all required fields');
      return;
    }

    updateTranslationMutation.mutate({
      id: editingTranslation.id,
      title: translatedTitle,
      description: translatedDescription
    });
  };

  const startEditing = (translation: BusinessTranslation) => {
    setEditingTranslation(translation);
    setTranslatedTitle(translation.title || '');
    setTranslatedDescription(translation.description || '');
  };

  const cancelEditing = () => {
    setEditingTranslation(null);
    setTranslatedTitle('');
    setTranslatedDescription('');
  };

  const getLanguageName = (code: string) => {
    return SUPPORTED_LANGUAGES.find(lang => lang.code === code)?.name || code;
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Globe className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600">Please sign in to manage translations</p>
        </CardContent>
      </Card>
    );
  }

  const selectedBusiness = userBusinesses?.find(b => b.id === selectedBusinessId);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Languages className="h-5 w-5 text-[#007749]" />
            Translation Manager
          </CardTitle>
          <CardDescription>
            Manage multilingual content for your business listings to reach more customers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Select Business</label>
              <Select value={selectedBusinessId} onValueChange={setSelectedBusinessId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose your business to translate" />
                </SelectTrigger>
                <SelectContent>
                  {userBusinesses?.map((business) => (
                    <SelectItem key={business.id} value={business.id}>
                      {business.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedBusiness && (
              <div className="bg-slate-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Original Content (English)</h4>
                <p className="font-medium text-sm mb-1">Title:</p>
                <p className="text-sm mb-3 text-slate-700">{selectedBusiness.title}</p>
                {selectedBusiness.description && (
                  <>
                    <p className="font-medium text-sm mb-1">Description:</p>
                    <p className="text-sm text-slate-700">{selectedBusiness.description}</p>
                  </>
                )}
              </div>
            )}

            {selectedBusinessId && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Target Language</label>
                    <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose language" />
                      </SelectTrigger>
                      <SelectContent>
                        {SUPPORTED_LANGUAGES.map((language) => (
                          <SelectItem key={language.code} value={language.code}>
                            {language.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Translated Title *</label>
                  <Input
                    value={translatedTitle}
                    onChange={(e) => setTranslatedTitle(e.target.value)}
                    placeholder="Enter translated title..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Translated Description</label>
                  <Textarea
                    value={translatedDescription}
                    onChange={(e) => setTranslatedDescription(e.target.value)}
                    placeholder="Enter translated description..."
                    rows={4}
                  />
                </div>

                <div className="flex items-center gap-2">
                  {editingTranslation ? (
                    <>
                      <Button
                        onClick={handleUpdateTranslation}
                        disabled={updateTranslationMutation.isPending}
                        className="bg-[#007749] hover:bg-[#006739]"
                      >
                        Update Translation
                      </Button>
                      <Button
                        onClick={cancelEditing}
                        variant="outline"
                      >
                        Cancel
                      </Button>
                    </>
                  ) : (
                    <Button
                      onClick={handleCreateTranslation}
                      disabled={createTranslationMutation.isPending}
                      className="bg-[#007749] hover:bg-[#006739]"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Translation
                    </Button>
                  )}
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Existing Translations */}
      {selectedBusinessId && translations && translations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Existing Translations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {translations.map((translation) => (
                <div key={translation.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {getLanguageName(translation.language_code)}
                      </Badge>
                      {translation.is_verified ? (
                        <Badge className="bg-green-100 text-green-800">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Verified
                        </Badge>
                      ) : (
                        <Badge className="bg-yellow-100 text-yellow-800">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          Unverified
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() => startEditing(translation)}
                        variant="ghost"
                        size="sm"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        onClick={() => deleteTranslationMutation.mutate(translation.id)}
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div>
                      <p className="text-sm font-medium text-slate-600">Title:</p>
                      <p className="text-sm">{translation.title}</p>
                    </div>
                    {translation.description && (
                      <div>
                        <p className="text-sm font-medium text-slate-600">Description:</p>
                        <p className="text-sm">{translation.description}</p>
                      </div>
                    )}
                  </div>
                  
                  <div className="mt-3 text-xs text-slate-500">
                    Quality: {(translation.translation_quality * 100).toFixed(0)}% • 
                    Created: {new Date(translation.created_at).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TranslationManager;
