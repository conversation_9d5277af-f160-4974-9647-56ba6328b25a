
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart
} from 'recharts';
import { 
  Eye, 
  MousePointer, 
  TrendingUp, 
  DollarSign,
  Calendar,
  Activity,
  Target,
  Users,
  Globe,
  Clock
} from 'lucide-react';

interface AdData {
  id: string;
  title: string;
  ad_type: string;
  position: string;
  status: string;
  impressions: number;
  clicks: number;
  budget_spent: number;
  budget_total: number;
  start_date: string;
  end_date: string | null;
  created_at: string;
}

const AdPerformanceDashboard = () => {
  const { user } = useAuth();
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');
  const [selectedAdId, setSelectedAdId] = useState<string | null>(null);

  // Get user's business listings
  const { data: userBusinesses } = useQuery({
    queryKey: ['user-businesses', user?.id],
    queryFn: async () => {
      if (!user) return [];
      const { data, error } = await supabase
        .from('business_listings')
        .select('id, title')
        .eq('user_id', user.id);
      if (error) throw error;
      return data;
    },
    enabled: !!user,
  });

  // Get ads performance data
  const { data: adsData, isLoading } = useQuery({
    queryKey: ['ads-performance', user?.id, selectedTimeRange],
    queryFn: async () => {
      if (!user || !userBusinesses || userBusinesses.length === 0) return [];

      const businessIds = userBusinesses.map(b => b.id);
      const { data, error } = await supabase
        .from('ads')
        .select('*')
        .in('business_id', businessIds)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as AdData[];
    },
    enabled: !!user && !!userBusinesses && userBusinesses.length > 0,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Activity className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600">Sign in to view your ad performance</p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#007749] mx-auto mb-4"></div>
          <p className="text-slate-600">Loading performance data...</p>
        </CardContent>
      </Card>
    );
  }

  if (!adsData || adsData.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <BarChart className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600 mb-2">No ads found</p>
          <p className="text-sm text-slate-500">Create your first ad campaign to see performance data</p>
        </CardContent>
      </Card>
    );
  }

  // Calculate summary metrics
  const totalImpressions = adsData.reduce((sum, ad) => sum + ad.impressions, 0);
  const totalClicks = adsData.reduce((sum, ad) => sum + ad.clicks, 0);
  const totalSpent = adsData.reduce((sum, ad) => sum + ad.budget_spent, 0);
  const avgCtr = totalImpressions > 0 ? ((totalClicks / totalImpressions) * 100).toFixed(2) : '0.00';
  const avgCpc = totalClicks > 0 ? (totalSpent / totalClicks / 100).toFixed(2) : '0.00';

  // Prepare chart data
  const performanceChartData = adsData.map(ad => ({
    name: ad.title.substring(0, 20) + (ad.title.length > 20 ? '...' : ''),
    impressions: ad.impressions,
    clicks: ad.clicks,
    ctr: totalImpressions > 0 ? ((ad.clicks / ad.impressions) * 100).toFixed(2) : '0.00',
    spent: ad.budget_spent / 100
  }));

  const COLORS = ['#007749', '#FDB913', '#0088FE', '#00C49F', '#FFBB28'];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-800">Ad Performance Dashboard</h2>
          <p className="text-slate-600">Monitor and analyze your advertising campaigns</p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">Total Impressions</p>
                <p className="text-3xl font-bold text-slate-900">{totalImpressions.toLocaleString()}</p>
              </div>
              <Eye className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">Total Clicks</p>
                <p className="text-3xl font-bold text-slate-900">{totalClicks.toLocaleString()}</p>
              </div>
              <MousePointer className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">Average CTR</p>
                <p className="text-3xl font-bold text-slate-900">{avgCtr}%</p>
              </div>
              <Target className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">Total Spent</p>
                <p className="text-3xl font-bold text-slate-900">R{(totalSpent / 100).toFixed(2)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="ads">Ad Details</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance by Ad</CardTitle>
                <CardDescription>Impressions and clicks comparison</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={performanceChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="impressions" fill="#007749" name="Impressions" />
                    <Bar dataKey="clicks" fill="#FDB913" name="Clicks" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Click-Through Rate</CardTitle>
                <CardDescription>CTR performance by ad</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={performanceChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="ctr" stroke="#007749" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="ads">
          <Card>
            <CardHeader>
              <CardTitle>Active Campaigns</CardTitle>
              <CardDescription>Detailed performance metrics for each ad</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {adsData.map((ad) => (
                  <Card key={ad.id} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-semibold text-lg">{ad.title}</h3>
                            <Badge className={getStatusColor(ad.status)}>
                              {ad.status}
                            </Badge>
                            <Badge variant="outline">{ad.position}</Badge>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                            <div>
                              <p className="text-slate-500">Impressions</p>
                              <p className="font-semibold">{ad.impressions.toLocaleString()}</p>
                            </div>
                            <div>
                              <p className="text-slate-500">Clicks</p>
                              <p className="font-semibold">{ad.clicks.toLocaleString()}</p>
                            </div>
                            <div>
                              <p className="text-slate-500">CTR</p>
                              <p className="font-semibold">{ad.impressions > 0 ? ((ad.clicks / ad.impressions) * 100).toFixed(2) : '0.00'}%</p>
                            </div>
                            <div>
                              <p className="text-slate-500">Budget</p>
                              <p className="font-semibold">R{(ad.budget_total / 100).toFixed(2)}</p>
                            </div>
                            <div>
                              <p className="text-slate-500">Spent</p>
                              <p className="font-semibold">R{(ad.budget_spent / 100).toFixed(2)}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdPerformanceDashboard;
