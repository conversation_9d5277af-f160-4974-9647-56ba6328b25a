
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';

const adSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  target_url: z.string().url('Must be a valid URL'),
  ad_type: z.string().min(1, 'Ad type is required'),
  position: z.string().min(1, 'Position is required'),
  budget_total: z.number().min(1, 'Budget must be at least 1'),
});

type AdFormData = z.infer<typeof adSchema>;

export const SimpleAdSubmissionForm: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [businessId, setBusinessId] = useState<string>('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<AdFormData>({
    resolver: zodResolver(adSchema),
    defaultValues: {
      ad_type: 'banner',
      position: 'top',
      budget_total: 100,
    },
  });

  const onSubmit = async (data: AdFormData) => {
    if (!user) {
      toast.error('Please sign in to submit an ad');
      return;
    }

    if (!businessId) {
      toast.error('Please select a business');
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase
        .from('ads')
        .insert({
          title: data.title,
          description: data.description,
          target_url: data.target_url,
          ad_type: data.ad_type,
          position: data.position,
          business_id: businessId,
          budget_total: data.budget_total * 100, // Convert to cents
          status: 'pending',
        });

      if (error) throw error;

      toast.success('Ad submitted successfully!');
      reset();
    } catch (error) {
      console.error('Error submitting ad:', error);
      toast.error('Failed to submit ad');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-slate-600">Please sign in to submit an ad</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Submit Advertisement</CardTitle>
        <CardDescription>
          Create a new advertisement for your business
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title">Ad Title</Label>
            <Input
              id="title"
              {...register('title')}
              placeholder="Enter your ad title"
            />
            {errors.title && (
              <p className="text-sm text-red-600">{errors.title.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Enter ad description (optional)"
              rows={3}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="target_url">Target URL</Label>
            <Input
              id="target_url"
              {...register('target_url')}
              placeholder="https://example.com"
            />
            {errors.target_url && (
              <p className="text-sm text-red-600">{errors.target_url.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="ad_type">Ad Type</Label>
              <Select onValueChange={(value) => setValue('ad_type', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select ad type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="banner">Banner</SelectItem>
                  <SelectItem value="sidebar">Sidebar</SelectItem>
                  <SelectItem value="sponsored">Sponsored Listing</SelectItem>
                </SelectContent>
              </Select>
              {errors.ad_type && (
                <p className="text-sm text-red-600">{errors.ad_type.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="position">Position</Label>
              <Select onValueChange={(value) => setValue('position', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select position" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="top">Top</SelectItem>
                  <SelectItem value="middle">Middle</SelectItem>
                  <SelectItem value="bottom">Bottom</SelectItem>
                  <SelectItem value="sidebar">Sidebar</SelectItem>
                </SelectContent>
              </Select>
              {errors.position && (
                <p className="text-sm text-red-600">{errors.position.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="budget_total">Budget (R)</Label>
            <Input
              id="budget_total"
              type="number"
              {...register('budget_total', { valueAsNumber: true })}
              placeholder="100"
              min="1"
            />
            {errors.budget_total && (
              <p className="text-sm text-red-600">{errors.budget_total.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="business_id">Business ID</Label>
            <Input
              id="business_id"
              value={businessId}
              onChange={(e) => setBusinessId(e.target.value)}
              placeholder="Enter your business ID"
            />
          </div>

          <Button type="submit" disabled={loading} className="w-full">
            {loading ? 'Submitting...' : 'Submit Ad'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
