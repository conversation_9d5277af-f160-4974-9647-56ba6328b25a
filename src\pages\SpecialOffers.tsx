
import React, { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Clock, Calendar, MapPin, Gift } from "lucide-react";
import { format } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Link } from "react-router-dom";

type SpecialOffer = {
  id: string;
  businessId: string;
  businessName: string;
  businessLogo?: string;
  title: string;
  description: string;
  discount: string;
  category: string;
  location: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  image: string;
  terms?: string;
};

const mockOffers: SpecialOffer[] = [
  {
    id: "1",
    businessId: "1",
    businessName: "Ocean Basket",
    businessLogo: "https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
    title: "Happy Hour: 50% Off Sushi Platters",
    description: "Enjoy half-price sushi platters every weekday from 4-6pm. Dine-in only.",
    discount: "50% OFF",
    category: "Restaurants",
    location: "Cape Town, Western Cape",
    startDate: new Date(2025, 4, 1, 16, 0), // May 1, 2025, 4:00 PM
    endDate: new Date(2025, 5, 30, 18, 0), // June 30, 2025, 6:00 PM
    isActive: true,
    image: "https://images.unsplash.com/photo-1579871494447-9811cf80d66c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    terms: "Valid for dine-in only. Cannot be combined with other offers."
  },
  {
    id: "2",
    businessId: "2",
    businessName: "The Capital Hotels",
    businessLogo: "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    title: "Weekend Getaway: 30% Off Suite Bookings",
    description: "Book any suite for weekend stays and get 30% off regular rates. Includes breakfast.",
    discount: "30% OFF",
    category: "Accommodation",
    location: "Sandton, Gauteng",
    startDate: new Date(2025, 4, 10), // May 10, 2025
    endDate: new Date(2025, 5, 15), // June 15, 2025
    isActive: true,
    image: "https://images.unsplash.com/photo-1578683010236-d716f9a3f461?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    terms: "Valid for Friday, Saturday and Sunday stays only. Subject to availability."
  },
  {
    id: "3",
    businessId: "5",
    businessName: "Sorbet Beauty Salon",
    businessLogo: "https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
    title: "Monday Manicure Special: R199",
    description: "Start your week with a professional manicure at our special Monday rate of R199 (regular price R350).",
    discount: "43% OFF",
    category: "Beauty & Wellness",
    location: "Durban, KwaZulu-Natal",
    startDate: new Date(2025, 4, 5, 9, 0), // May 5, 2025, 9:00 AM
    endDate: new Date(2025, 6, 28, 17, 0), // July 28, 2025, 5:00 PM
    isActive: true,
    image: "https://images.unsplash.com/photo-1600948836101-f9ffda59d250?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1472&q=80",
    terms: "Valid on Mondays only. Booking essential."
  },
  {
    id: "4",
    businessId: "3",
    businessName: "Woolworths",
    businessLogo: "https://images.unsplash.com/photo-1604719312566-8912e9227c6a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
    title: "Flash Sale: 25% Off All Winter Clothing",
    description: "Online and in-store flash sale! Get 25% off all winter clothing for 48 hours only.",
    discount: "25% OFF",
    category: "Retail",
    location: "Nationwide",
    startDate: new Date(2025, 4, 20, 0, 0), // May 20, 2025, 12:00 AM
    endDate: new Date(2025, 4, 21, 23, 59), // May 21, 2025, 11:59 PM
    isActive: false,
    image: "https://images.unsplash.com/photo-1483985988355-763728e1935b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    terms: "Cannot be combined with other promotions or discounts."
  },
  {
    id: "5",
    businessId: "6",
    businessName: "University of Cape Town",
    businessLogo: "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    title: "Early Bird: 15% Off Short Courses",
    description: "Register early for our winter short courses and save 15% on tuition fees.",
    discount: "15% OFF",
    category: "Education",
    location: "Cape Town, Western Cape",
    startDate: new Date(2025, 4, 1), // May 1, 2025
    endDate: new Date(2025, 4, 31), // May 31, 2025
    isActive: true,
    image: "https://images.unsplash.com/photo-**********-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    terms: "Payment must be made in full to qualify for the discount."
  },
  {
    id: "6",
    businessId: "4",
    businessName: "FNB Business",
    businessLogo: "https://images.unsplash.com/photo-*************-7e09a677fb2c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    title: "New Business Account: Zero Fees for 3 Months",
    description: "Open a new business account and pay no monthly fees for the first 3 months.",
    discount: "100% OFF",
    category: "Financial Services",
    location: "Johannesburg, Gauteng",
    startDate: new Date(2025, 4, 15), // May 15, 2025
    endDate: new Date(2025, 6, 15), // July 15, 2025
    isActive: true,
    image: "https://images.unsplash.com/photo-*************-0ad531db3366?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1476&q=80",
    terms: "Standard credit approval criteria apply. Offer valid for new customers only."
  }
];

const SpecialOffers = () => {
  const { data: offers = [], isLoading } = useQuery({
    queryKey: ['special-offers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('special_offers')
        .select('*')
        .eq('status', 'approved')
        .eq('is_active', true)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    }
  });

  const [currentTime] = useState(new Date());
  
  const isOfferActive = (offer: any): boolean => {
    return currentTime >= new Date(offer.start_date) && currentTime <= new Date(offer.end_date);
  };
  
  const activeOffers = offers.filter(isOfferActive);
  const upcomingOffers = offers.filter(offer => currentTime < new Date(offer.start_date));
  const expiredOffers = offers.filter(offer => currentTime > new Date(offer.end_date));
  
  return (
    <Layout>
      <div className="py-10">
        <div className="container mx-auto px-4">
          <div className="mb-8 text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
              Special Offers & Promotions
            </h1>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto mb-6">
              Limited-time deals and promotions from top businesses across South Africa
            </p>
            <Link to="/add-special-offer">
              <Button className="bg-[#007749] hover:bg-[#006739]">
                Submit Your Offer
              </Button>
            </Link>
          </div>

          <Tabs defaultValue="active" className="mb-8" onValueChange={setActiveTab}>
            <div className="flex justify-center mb-6">
              <TabsList>
                <TabsTrigger value="active">
                  Active Now ({activeOffers.length})
                </TabsTrigger>
                <TabsTrigger value="upcoming">
                  Upcoming ({upcomingOffers.length})
                </TabsTrigger>
                <TabsTrigger value="expired">
                  Expired ({expiredOffers.length})
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="active" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {activeOffers.map((offer) => (
                  <OfferCard 
                    key={offer.id} 
                    offer={offer}
                    status="active"
                  />
                ))}
              </div>
              
              {activeOffers.length === 0 && (
                <div className="text-center py-12">
                  <Gift className="h-16 w-16 mx-auto text-slate-300 mb-4" />
                  <h3 className="text-xl font-medium text-slate-600">No active offers right now</h3>
                  <p className="text-slate-500 mt-2">Check back soon for new deals!</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="upcoming" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {upcomingOffers.map((offer) => (
                  <OfferCard 
                    key={offer.id} 
                    offer={offer}
                    status="upcoming"
                  />
                ))}
              </div>
              
              {upcomingOffers.length === 0 && (
                <div className="text-center py-12">
                  <Calendar className="h-16 w-16 mx-auto text-slate-300 mb-4" />
                  <h3 className="text-xl font-medium text-slate-600">No upcoming offers</h3>
                  <p className="text-slate-500 mt-2">Check back soon for new deals!</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="expired" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {expiredOffers.map((offer) => (
                  <OfferCard 
                    key={offer.id} 
                    offer={offer}
                    status="expired"
                  />
                ))}
              </div>
              
              {expiredOffers.length === 0 && (
                <div className="text-center py-12">
                  <Clock className="h-16 w-16 mx-auto text-slate-300 mb-4" />
                  <h3 className="text-xl font-medium text-slate-600">No expired offers</h3>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

interface OfferCardProps {
  offer: SpecialOffer;
  status: "active" | "upcoming" | "expired";
}

const OfferCard = ({ offer, status }: OfferCardProps) => {
  const getStatusBadge = () => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-600">Active Now</Badge>;
      case "upcoming":
        return <Badge className="bg-blue-600">Coming Soon</Badge>;
      case "expired":
        return <Badge className="bg-slate-500">Expired</Badge>;
      default:
        return null;
    }
  };

  const formatDateRange = () => {
    // For deals with specific hours (like happy hours)
    if (offer.startDate.getHours() !== 0) {
      return (
        <>
          <Clock className="h-4 w-4 text-slate-500" />
          <span>{format(offer.startDate, "h:mm a")} - {format(offer.endDate, "h:mm a")}</span>
          <span className="mx-1">•</span>
          <span>{format(offer.startDate, "MMM d")} - {format(offer.endDate, "MMM d, yyyy")}</span>
        </>
      );
    }
    
    // For regular date ranges
    return (
      <>
        <Calendar className="h-4 w-4 text-slate-500" />
        <span>{format(offer.startDate, "MMM d")} - {format(offer.endDate, "MMM d, yyyy")}</span>
      </>
    );
  };

  return (
    <Card className="overflow-hidden h-full flex flex-col">
      <div className="relative">
        <img 
          src={offer.image} 
          alt={offer.title} 
          className="w-full aspect-[16/9] object-cover"
        />
        <div className="absolute top-0 left-0 right-0 p-3 flex justify-between">
          <Badge className="bg-[#FDB913] text-black font-bold">
            {offer.discount}
          </Badge>
          {getStatusBadge()}
        </div>
      </div>
      
      <CardContent className="flex-1 flex flex-col p-5">
        <div className="flex items-center mb-3">
          {offer.businessLogo && (
            <img 
              src={offer.businessLogo} 
              alt={offer.businessName}
              className="h-8 w-8 object-cover rounded-full mr-2"
            />
          )}
          <span className="font-medium">{offer.businessName}</span>
        </div>
        
        <h3 className="font-semibold text-lg text-slate-800 mb-2">
          {offer.title}
        </h3>
        
        <p className="text-slate-600 text-sm mb-4 flex-1">{offer.description}</p>
        
        <div className="flex items-center text-sm text-slate-500 mb-3">
          <MapPin className="h-4 w-4 mr-1" />
          {offer.location}
        </div>
        
        <div className="flex items-center text-sm text-slate-500 mb-4">
          {formatDateRange()}
        </div>
        
        {offer.terms && (
          <p className="text-xs text-slate-400 mb-4">
            <strong>Terms:</strong> {offer.terms}
          </p>
        )}
        
        <div className="mt-auto">
          <Button 
            className="w-full bg-[#007749] hover:bg-[#006739]"
            disabled={status === "expired"}
          >
            {status === "active" ? "View Offer" : status === "upcoming" ? "Remind Me" : "Expired"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default SpecialOffers;
