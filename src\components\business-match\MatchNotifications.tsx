
import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Bell, BellOff, Eye, EyeOff, Star, MapPin } from 'lucide-react';
import { toast } from 'sonner';

interface MockNotification {
  id: string;
  business_id: string;
  match_score: number;
  notification_type: string;
  message: string | null;
  is_read: boolean;
  sent_at: string;
  business_listings: {
    title: string;
    description: string | null;
    city: string | null;
    logo_url: string | null;
    categories?: {
      name: string;
    };
  };
}

const MatchNotifications = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<MockNotification[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      // Load mock notifications since the database tables don't exist yet
      loadMockNotifications();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  const loadMockNotifications = async () => {
    try {
      // Get some sample businesses to create mock notifications
      const { data: businesses } = await supabase
        .from('business_listings')
        .select(`
          id,
          title,
          description,
          city,
          logo_url,
          categories (
            name
          )
        `)
        .eq('status', 'approved')
        .limit(3);

      if (businesses) {
        const mockNotifications: MockNotification[] = businesses.map((business, index) => ({
          id: `notification_${index}`,
          business_id: business.id,
          match_score: 85 + (index * 5),
          notification_type: index === 0 ? 'new_match' : index === 1 ? 'updated_match' : 'special_offer',
          message: index === 0 ? 'New business match found!' : index === 1 ? 'Business updated their profile' : 'Special offer available',
          is_read: index > 0,
          sent_at: new Date(Date.now() - (index * 86400000)).toISOString(),
          business_listings: {
            title: business.title,
            description: business.description,
            city: business.city,
            logo_url: business.logo_url,
            categories: business.categories
          }
        }));

        setNotifications(mockNotifications);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      // Update local state since we're using mock data
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, is_read: true }
            : notification
        )
      );
      
      toast.success('Notification marked as read');
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Failed to update notification');
    }
  };

  const markAllAsRead = async () => {
    try {
      // Update local state since we're using mock data
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, is_read: true }))
      );
      
      toast.success('All notifications marked as read');
    } catch (error) {
      console.error('Error marking all as read:', error);
      toast.error('Failed to update notifications');
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_match':
        return <Star className="h-4 w-4 text-[#007749]" />;
      case 'updated_match':
        return <Bell className="h-4 w-4 text-blue-600" />;
      case 'special_offer':
        return <Badge className="h-4 w-4 text-orange-600" />;
      default:
        return <Bell className="h-4 w-4 text-slate-600" />;
    }
  };

  const getNotificationTypeLabel = (type: string) => {
    switch (type) {
      case 'new_match':
        return 'New Match';
      case 'updated_match':
        return 'Updated Match';
      case 'special_offer':
        return 'Special Offer';
      default:
        return 'Notification';
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <BellOff className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600">Sign in to view your match notifications</p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#007749] mx-auto mb-4"></div>
          <p className="text-slate-600">Loading notifications...</p>
        </CardContent>
      </Card>
    );
  }

  const unreadCount = notifications.filter(n => !n.is_read).length;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-[#007749]" />
              Match Notifications
              {unreadCount > 0 && (
                <Badge className="bg-red-100 text-red-800">
                  {unreadCount} new
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Get notified about new business matches and updates
            </CardDescription>
          </div>
          {unreadCount > 0 && (
            <Button variant="outline" size="sm" onClick={markAllAsRead}>
              <EyeOff className="h-4 w-4 mr-2" />
              Mark all read
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {notifications.length === 0 ? (
          <div className="text-center py-8">
            <BellOff className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-600 mb-2">No notifications yet</p>
            <p className="text-sm text-slate-500">
              Complete a business match to start receiving personalized recommendations
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {notifications.map((notification) => (
              <Card 
                key={notification.id} 
                className={`cursor-pointer transition-colors ${
                  !notification.is_read ? 'bg-blue-50 border-blue-200' : ''
                }`}
                onClick={() => !notification.is_read && markAsRead(notification.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start gap-4">
                    {notification.business_listings.logo_url ? (
                      <img 
                        src={notification.business_listings.logo_url} 
                        alt={notification.business_listings.title}
                        className="w-12 h-12 object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-slate-100 rounded-lg flex items-center justify-center">
                        {getNotificationIcon(notification.notification_type)}
                      </div>
                    )}

                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          {getNotificationTypeLabel(notification.notification_type)}
                        </Badge>
                        <Badge className="bg-[#007749] text-white text-xs">
                          {notification.match_score}% match
                        </Badge>
                        {!notification.is_read && (
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                        )}
                      </div>

                      <h3 className="font-semibold text-slate-800 mb-1">
                        {notification.business_listings.title}
                      </h3>

                      {notification.message && (
                        <p className="text-sm text-slate-600 mb-2">
                          {notification.message}
                        </p>
                      )}

                      <div className="flex items-center gap-4 text-xs text-slate-500">
                        {notification.business_listings.categories && (
                          <span>{notification.business_listings.categories.name}</span>
                        )}
                        {notification.business_listings.city && (
                          <span className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {notification.business_listings.city}
                          </span>
                        )}
                        <span>{new Date(notification.sent_at).toLocaleDateString()}</span>
                      </div>
                    </div>

                    {!notification.is_read && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          markAsRead(notification.id);
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MatchNotifications;
