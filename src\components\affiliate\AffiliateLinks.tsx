
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, Shield, CreditCard, Settings, TrendingUp } from 'lucide-react';

interface AffiliateLinksProps {
  context: 'business_listing' | 'category_page' | 'homepage';
  category?: string;
  businessId?: string;
  className?: string;
}

const AffiliateLinks = ({ context, category, businessId, className }: AffiliateLinksProps) => {
  // For now, we'll show some static affiliate links since the affiliate_links table doesn't exist
  const staticAffiliateLinks = [
    {
      id: '1',
      title: 'Business Banking Solutions',
      description: 'Get the best banking services for your business with competitive rates',
      affiliate_url: 'https://example.com/banking',
      category: 'banking',
      priority: 5,
      partner_name: 'Business Bank',
      partner_logo: null
    },
    {
      id: '2',
      title: 'Professional Insurance',
      description: 'Protect your business with comprehensive insurance coverage',
      affiliate_url: 'https://example.com/insurance',
      category: 'insurance',
      priority: 4,
      partner_name: 'InsureCorp',
      partner_logo: null
    },
    {
      id: '3',
      title: 'Marketing Tools',
      description: 'Boost your online presence with advanced marketing solutions',
      affiliate_url: 'https://example.com/marketing',
      category: 'marketing',
      priority: 3,
      partner_name: 'MarketPro',
      partner_logo: null
    },
    {
      id: '4',
      title: 'Business Services',
      description: 'Streamline your operations with professional business services',
      affiliate_url: 'https://example.com/services',
      category: 'business_services',
      priority: 2,
      partner_name: 'BizServices',
      partner_logo: null
    }
  ];

  // Filter by category if specified
  const filteredLinks = category 
    ? staticAffiliateLinks.filter(link => link.category === category)
    : staticAffiliateLinks;

  const trackClick = (linkId: string) => {
    // For now, just log the click since affiliate_clicks table doesn't exist
    console.log('Affiliate link clicked:', linkId);
  };

  const handleLinkClick = (link: any) => {
    trackClick(link.id);
    window.open(link.affiliate_url, '_blank', 'noopener,noreferrer');
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'banking':
        return <CreditCard className="h-4 w-4" />;
      case 'insurance':
        return <Shield className="h-4 w-4" />;
      case 'business_services':
        return <Settings className="h-4 w-4" />;
      case 'marketing':
        return <TrendingUp className="h-4 w-4" />;
      default:
        return <ExternalLink className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'banking':
        return 'bg-blue-100 text-blue-800';
      case 'insurance':
        return 'bg-green-100 text-green-800';
      case 'business_services':
        return 'bg-purple-100 text-purple-800';
      case 'marketing':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (filteredLinks.length === 0) {
    return null;
  }

  return (
    <div className={`affiliate-links ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-slate-800">Recommended Services</h3>
        <Badge variant="outline" className="text-xs">
          Partner Recommendations
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {filteredLinks.map((link) => (
          <Card 
            key={link.id}
            className="cursor-pointer hover:shadow-md transition-all duration-200 border-l-4 border-l-[#007749]"
            onClick={() => handleLinkClick(link)}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-slate-100 rounded flex items-center justify-center">
                    {getCategoryIcon(link.category)}
                  </div>
                  <Badge className={getCategoryColor(link.category)}>
                    {getCategoryIcon(link.category)}
                    <span className="ml-1 capitalize">{link.category.replace('_', ' ')}</span>
                  </Badge>
                </div>
                <ExternalLink className="h-4 w-4 text-slate-400" />
              </div>

              <div>
                <h4 className="font-semibold text-slate-800 mb-1">{link.title}</h4>
                <p className="text-sm text-slate-600 mb-2">{link.description}</p>
                <p className="text-xs text-slate-500">by {link.partner_name}</p>
              </div>

              <div className="mt-3 pt-3 border-t border-slate-100">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full text-[#007749] border-[#007749] hover:bg-[#007749] hover:text-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleLinkClick(link);
                  }}
                >
                  Learn More
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-4 text-center">
        <p className="text-xs text-slate-500">
          These are partner recommendations. SA360 may earn a commission from qualifying purchases.
        </p>
      </div>
    </div>
  );
};

export default AffiliateLinks;
