
import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface TrackingData {
  adId: string;
  eventType: 'impression' | 'click';
  viewDuration?: number;
  referrer?: string;
  pageUrl?: string;
}

export const useAdTracking = () => {
  const { user } = useAuth();

  const generateSessionId = useCallback(() => {
    return Math.random().toString(36).substr(2, 9);
  }, []);

  const getDeviceInfo = useCallback(() => {
    if (typeof window === 'undefined') return { deviceType: 'desktop', browser: 'unknown' };

    const userAgent = navigator.userAgent.toLowerCase();
    let deviceType = 'desktop';
    let browser = 'other';

    // Detect device type
    if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
      deviceType = 'mobile';
    } else if (userAgent.includes('tablet') || userAgent.includes('ipad')) {
      deviceType = 'tablet';
    }

    // Detect browser
    if (userAgent.includes('chrome')) {
      browser = 'Chrome';
    } else if (userAgent.includes('firefox')) {
      browser = 'Firefox';
    } else if (userAgent.includes('safari')) {
      browser = 'Safari';
    } else if (userAgent.includes('edge')) {
      browser = 'Edge';
    }

    return { deviceType, browser };
  }, []);

  const trackImpression = useCallback(async (data: TrackingData) => {
    try {
      const { deviceType, browser } = getDeviceInfo();
      
      console.log('Tracking impression:', {
        adId: data.adId,
        userId: user?.id,
        deviceType,
        browser,
        pageUrl: data.pageUrl || (typeof window !== 'undefined' ? window.location.href : null),
        viewDuration: data.viewDuration || 0
      });

      // Get current impressions count first
      const { data: adData, error: fetchError } = await supabase
        .from('ads')
        .select('impressions')
        .eq('id', data.adId)
        .single();

      if (fetchError) {
        console.error('Error fetching ad data:', fetchError);
        return;
      }

      // Update with incremented value
      const { error } = await supabase
        .from('ads')
        .update({ 
          impressions: (adData?.impressions || 0) + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.adId);

      if (error) {
        console.error('Error updating impression count:', error);
      }
    } catch (error) {
      console.error('Error tracking impression:', error);
    }
  }, [user, getDeviceInfo]);

  const trackClick = useCallback(async (data: TrackingData) => {
    try {
      const { deviceType, browser } = getDeviceInfo();
      
      console.log('Tracking click:', {
        adId: data.adId,
        userId: user?.id,
        deviceType,
        browser,
        referrer: data.referrer || (typeof window !== 'undefined' ? document.referrer : null),
        pageUrl: data.pageUrl || (typeof window !== 'undefined' ? window.location.href : null)
      });

      // Get current clicks count first
      const { data: adData, error: fetchError } = await supabase
        .from('ads')
        .select('clicks')
        .eq('id', data.adId)
        .single();

      if (fetchError) {
        console.error('Error fetching ad data:', fetchError);
        return;
      }

      // Update with incremented value
      const { error } = await supabase
        .from('ads')
        .update({ 
          clicks: (adData?.clicks || 0) + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.adId);

      if (error) {
        console.error('Error updating click count:', error);
      }
    } catch (error) {
      console.error('Error tracking click:', error);
    }
  }, [user, getDeviceInfo]);

  const trackConversion = useCallback(async (adId: string, conversionValue?: number) => {
    try {
      console.log('Conversion tracked for ad:', adId, 'value:', conversionValue);
      
      // In a real implementation, you might:
      // 1. Update a conversions table
      // 2. Calculate conversion rates
      // 3. Update campaign performance metrics
    } catch (error) {
      console.error('Error tracking conversion:', error);
    }
  }, []);

  const getAdPerformance = useCallback(async (adId: string, timeRange: string = '7d') => {
    try {
      // Get basic ad performance from the existing ads table
      const { data: adData, error } = await supabase
        .from('ads')
        .select('impressions, clicks, created_at')
        .eq('id', adId)
        .single();

      if (error) {
        console.error('Error getting ad performance:', error);
        return null;
      }

      const totalImpressions = adData?.impressions || 0;
      const totalClicks = adData?.clicks || 0;
      const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;

      // Mock data for device and browser breakdown since we don't have detailed tracking tables yet
      const deviceBreakdown = {
        mobile: Math.floor(totalClicks * 0.6),
        desktop: Math.floor(totalClicks * 0.3),
        tablet: Math.floor(totalClicks * 0.1)
      };

      const browserBreakdown = {
        Chrome: Math.floor(totalClicks * 0.5),
        Safari: Math.floor(totalClicks * 0.3),
        Firefox: Math.floor(totalClicks * 0.2)
      };

      // Mock hourly data
      const hourlyData = Array.from({ length: 24 }, (_, hour) => ({
        hour,
        clicks: Math.floor(Math.random() * 10)
      }));

      return {
        totalImpressions,
        totalClicks,
        ctr: parseFloat(ctr.toFixed(2)),
        deviceBreakdown,
        browserBreakdown,
        hourlyData
      };
    } catch (error) {
      console.error('Error getting ad performance:', error);
      return null;
    }
  }, []);

  return {
    trackImpression,
    trackClick,
    trackConversion,
    getAdPerformance,
    getDeviceInfo
  };
};

export default useAdTracking;
