import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Calendar, 
  MapPin, 
  Clock, 
  Plus,
  ExternalLink,
  Star,
  Users,
  Target
} from 'lucide-react';
import { toast } from 'sonner';

interface Event {
  id: string;
  title: string;
  description: string | null;
  event_type: string;
  start_date: string;
  end_date: string | null;
  location: string;
  city: string;
  featured: boolean;
}

interface BusinessEventConnection {
  id: string;
  connection_type: string;
  description: string | null;
  special_offer: string | null;
  discount_code: string | null;
  is_featured: boolean;
}

const BusinessEventOpportunities = () => {
  const { user } = useAuth();
  const [selectedBusinessId, setSelectedBusinessId] = useState<string>('');
  const [connectionType, setConnectionType] = useState<string>('sponsor');
  const [description, setDescription] = useState<string>('');
  const [specialOffer, setSpecialOffer] = useState<string>('');
  const [discountCode, setDiscountCode] = useState<string>('');

  const { data: userBusinesses } = useQuery({
    queryKey: ['user-businesses', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      const { data, error } = await supabase
        .from('business_listings')
        .select('id, title')
        .eq('user_id', user.id)
        .eq('status', 'approved');

      if (error) throw error;
      return data || [];
    },
    enabled: !!user?.id,
  });

  const { data: upcomingEvents, isLoading: eventsLoading } = useQuery({
    queryKey: ['upcoming-events'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('events')
          .select('*')
          .eq('status', 'published')
          .gte('start_date', new Date().toISOString())
          .order('start_date', { ascending: true })
          .limit(20);

        if (error) {
          console.error('Error fetching events:', error);
          return [];
        }
        return (data || []) as Event[];
      } catch (error) {
        console.error('Error in events query:', error);
        return [];
      }
    },
  });

  const { data: businessConnections, refetch: refetchConnections } = useQuery({
    queryKey: ['business-event-connections', selectedBusinessId],
    queryFn: async () => {
      if (!selectedBusinessId) return [];
      
      try {
        const { data, error } = await supabase
          .from('business_event_connections')
          .select(`
            *,
            events (
              title,
              start_date,
              location
            )
          `)
          .eq('business_id', selectedBusinessId);

        if (error) {
          console.error('Error fetching connections:', error);
          return [];
        }
        return (data || []) as BusinessEventConnection[];
      } catch (error) {
        console.error('Error in connections query:', error);
        return [];
      }
    },
    enabled: !!selectedBusinessId,
  });

  const createEventConnection = async (eventId: string) => {
    if (!selectedBusinessId || !description) {
      toast.error('Please select a business and provide a description');
      return;
    }

    try {
      const { error } = await supabase
        .from('business_event_connections')
        .insert({
          business_id: selectedBusinessId,
          event_id: eventId,
          connection_type: connectionType,
          description,
          special_offer: specialOffer || null,
          discount_code: discountCode || null,
          is_featured: false
        });

      if (error) throw error;

      toast.success('Event connection created successfully!');
      setDescription('');
      setSpecialOffer('');
      setDiscountCode('');
      refetchConnections();
    } catch (error) {
      console.error('Error creating connection:', error);
      toast.error('Failed to create event connection');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Users className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600">Please sign in to view business event opportunities</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-[#007749]" />
            Business Event Opportunities
          </CardTitle>
          <CardDescription>
            Connect your business with local events through sponsorships, partnerships, and collaborations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Select Business</label>
              <Select value={selectedBusinessId} onValueChange={setSelectedBusinessId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose your business" />
                </SelectTrigger>
                <SelectContent>
                  {userBusinesses?.map((business) => (
                    <SelectItem key={business.id} value={business.id}>
                      {business.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Connection Type</label>
                <Select value={connectionType} onValueChange={setConnectionType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sponsor">Sponsor</SelectItem>
                    <SelectItem value="partner">Partner</SelectItem>
                    <SelectItem value="vendor">Vendor</SelectItem>
                    <SelectItem value="exhibitor">Exhibitor</SelectItem>
                    <SelectItem value="speaker">Speaker</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Discount Code (Optional)</label>
                <Input
                  value={discountCode}
                  onChange={(e) => setDiscountCode(e.target.value)}
                  placeholder="e.g., EVENT20"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Connection Description</label>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe how your business will participate in events..."
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Special Offer (Optional)</label>
              <Input
                value={specialOffer}
                onChange={(e) => setSpecialOffer(e.target.value)}
                placeholder="e.g., 20% off for event attendees"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Upcoming Events</h2>
        
        {eventsLoading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-slate-200 animate-pulse rounded-lg h-32"></div>
            ))}
          </div>
        ) : !upcomingEvents || upcomingEvents.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Calendar className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-600">No upcoming events available</p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {upcomingEvents.map((event) => (
              <Card key={event.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold text-lg">{event.title}</h3>
                        {event.featured && (
                          <Badge className="bg-[#FDB913] text-black">
                            <Star className="h-3 w-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-slate-600 mb-3">
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {formatDate(event.start_date)}
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {event.city}
                        </div>
                        <Badge variant="outline">
                          {event.event_type}
                        </Badge>
                      </div>
                      
                      {event.description && (
                        <p className="text-slate-700 text-sm line-clamp-2 mb-3">
                          {event.description}
                        </p>
                      )}
                    </div>
                    
                    <Button
                      onClick={() => createEventConnection(event.id)}
                      disabled={!selectedBusinessId || !description}
                      className="bg-[#007749] hover:bg-[#006739] ml-4"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Connect
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {selectedBusinessId && businessConnections && businessConnections.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Your Event Connections</h2>
          <div className="grid gap-4">
            {businessConnections.map((connection) => (
              <Card key={connection.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <Badge className={connection.is_featured ? 'bg-[#FDB913] text-black' : 'bg-slate-100'}>
                          {connection.connection_type}
                        </Badge>
                        {connection.is_featured && (
                          <Badge className="bg-green-100 text-green-800">Featured</Badge>
                        )}
                      </div>
                      <p className="text-sm text-slate-600">{connection.description}</p>
                      {connection.special_offer && (
                        <p className="text-sm font-medium text-[#007749] mt-1">
                          Offer: {connection.special_offer}
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessEventOpportunities;
