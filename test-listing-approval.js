/**
 * Test script for the listing approval email notification system
 * 
 * This script helps verify that the email notification system is working correctly.
 * Run with: node test-listing-approval.js
 * 
 * Prerequisites:
 * 1. Supabase project is set up with the migration applied
 * 2. Edge functions are deployed
 * 3. Resend API key is configured
 * 4. npm install @supabase/supabase-js
 */

import { createClient } from '@supabase/supabase-js';

// Configuration - Update these with your actual values
const SUPABASE_URL = 'https://your-project.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'your-service-role-key';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function testListingApprovalSystem() {
  console.log('🚀 Starting Listing Approval System Test\n');

  try {
    // Step 1: Create a test user (if needed)
    console.log('1. Creating test user...');
    const testUser = await createTestUser();
    console.log('✅ Test user created:', testUser.email);

    // Step 2: Create a test category (if needed)
    console.log('\n2. Ensuring test category exists...');
    const testCategory = await ensureTestCategory();
    console.log('✅ Test category ready:', testCategory.name);

    // Step 3: Create a test listing
    console.log('\n3. Creating test listing...');
    const testListing = await createTestListing(testUser.id, testCategory.id);
    console.log('✅ Test listing created:', testListing.title);
    console.log('   Status:', testListing.status);
    console.log('   ID:', testListing.id);

    // Step 4: Check if email notification was queued
    console.log('\n4. Checking email notification queue...');
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds for trigger
    const emailNotification = await checkEmailNotification(testListing.id);
    if (emailNotification) {
      console.log('✅ Email notification queued successfully');
      console.log('   Type:', emailNotification.type);
      console.log('   Recipient:', emailNotification.recipient_email);
      console.log('   Status:', emailNotification.status);
    } else {
      console.log('❌ No email notification found - check database trigger');
      return;
    }

    // Step 5: Process email notifications
    console.log('\n5. Processing email notifications...');
    const processResult = await processEmailNotifications();
    console.log('✅ Email processing result:', processResult);

    // Step 6: Check if email was sent
    console.log('\n6. Verifying email was sent...');
    const updatedNotification = await checkEmailNotification(testListing.id);
    if (updatedNotification && updatedNotification.status === 'sent') {
      console.log('✅ Email sent successfully!');
      console.log('   Check <EMAIL> for the approval email');
    } else {
      console.log('❌ Email was not sent - check Resend configuration');
      console.log('   Current status:', updatedNotification?.status);
      if (updatedNotification?.error_message) {
        console.log('   Error:', updatedNotification.error_message);
      }
    }

    // Step 7: Test approval token generation
    console.log('\n7. Checking approval token...');
    const approvalToken = await checkApprovalToken(testListing.id);
    if (approvalToken) {
      console.log('✅ Approval token generated');
      console.log('   Token expires at:', approvalToken.expires_at);
      
      // Generate test approval URL
      const testApprovalUrl = `${SUPABASE_URL}/functions/v1/approve-listing?token=${approvalToken.token}&action=approve`;
      console.log('   Test approval URL:', testApprovalUrl);
      console.log('   (You can test this URL in a browser)');
    } else {
      console.log('❌ No approval token found');
    }

    console.log('\n🎉 Test completed! Summary:');
    console.log('- Test listing created with pending status');
    console.log('- Email notification queued automatically');
    console.log('- Email processing system working');
    console.log('- Approval token system ready');
    console.log('\nNext steps:');
    console.log('1. Check your <NAME_EMAIL>');
    console.log('2. Test the approval links in the email');
    console.log('3. Verify listing status changes after approval');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

async function createTestUser() {
  // Check if test user already exists
  const { data: existingUser } = await supabase
    .from('profiles')
    .select('*')
    .eq('email', '<EMAIL>')
    .single();

  if (existingUser) {
    return existingUser;
  }

  // Create a new test user
  const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
    email: '<EMAIL>',
    password: 'testpassword123',
    email_confirm: true,
    user_metadata: {
      full_name: 'Test User'
    }
  });

  if (authError) {
    throw new Error(`Failed to create auth user: ${authError.message}`);
  }

  // The profile should be created automatically by the trigger
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', authUser.user.id)
    .single();

  return profile || { id: authUser.user.id, email: '<EMAIL>', full_name: 'Test User' };
}

async function ensureTestCategory() {
  // Check if test category exists
  const { data: existingCategory } = await supabase
    .from('categories')
    .select('*')
    .eq('slug', 'test-category')
    .single();

  if (existingCategory) {
    return existingCategory;
  }

  // Create test category
  const { data: newCategory, error } = await supabase
    .from('categories')
    .insert({
      name: 'Test Category',
      slug: 'test-category',
      description: 'Test category for approval system testing'
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create test category: ${error.message}`);
  }

  return newCategory;
}

async function createTestListing(userId, categoryId) {
  const { data: listing, error } = await supabase
    .from('business_listings')
    .insert({
      user_id: userId,
      title: `Test Listing ${Date.now()}`,
      category_id: categoryId,
      description: 'This is a test listing for the approval system',
      full_address: '123 Test Street',
      city: 'Cape Town',
      phone: '+***********',
      website: 'https://test-business.com',
      status: 'pending'
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create test listing: ${error.message}`);
  }

  return listing;
}

async function checkEmailNotification(listingId) {
  const { data: notification, error } = await supabase
    .from('email_notifications')
    .select('*')
    .eq('type', 'listing_approval')
    .contains('data', { listingId })
    .order('created_at', { ascending: false })
    .limit(1)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
    console.warn('Error checking email notification:', error.message);
  }

  return notification;
}

async function processEmailNotifications() {
  const { data, error } = await supabase.functions.invoke('process-email-notifications');

  if (error) {
    throw new Error(`Failed to process emails: ${error.message}`);
  }

  return data;
}

async function checkApprovalToken(listingId) {
  const { data: token, error } = await supabase
    .from('listing_approval_tokens')
    .select('*')
    .eq('listing_id', listingId)
    .order('created_at', { ascending: false })
    .limit(1)
    .single();

  if (error && error.code !== 'PGRST116') {
    console.warn('Error checking approval token:', error.message);
  }

  return token;
}

// Run the test
testListingApprovalSystem();
