# Troubleshooting Email Notification System

You're not receiving approval emails. Let's debug this step by step.

## Quick Diagnosis

Run these commands to quickly identify the issue:

### 1. Run the Debug Script
```bash
# First, update the debug script with your actual credentials
node debug-email-system.js
```

### 2. Check Database Directly
Run the SQL queries in `debug-email-system.sql` in your Supabase SQL editor.

## Step-by-Step Troubleshooting

### Step 1: Verify Migration Applied

**Check if the migration was applied:**
```sql
SELECT version, name, applied_at 
FROM supabase_migrations.schema_migrations 
WHERE name LIKE '%listing-approval%' 
ORDER BY applied_at DESC;
```

**If no results:** The migration wasn't applied. Run:
```bash
supabase db push
```

### Step 2: Check Tables Exist

**Run this query:**
```sql
SELECT tablename FROM pg_tables 
WHERE tablename IN ('email_notifications', 'listing_approval_tokens');
```

**Expected result:** Both tables should be listed.

**If missing:** Re-run the migration:
```bash
supabase db reset
supabase db push
```

### Step 3: Check Database Trigger

**Check if trigger function exists:**
```sql
SELECT proname FROM pg_proc WHERE proname = 'send_listing_approval_email';
```

**Check if trigger exists:**
```sql
SELECT tgname, tgenabled FROM pg_trigger 
WHERE tgname = 'trigger_send_listing_approval_email';
```

**If missing:** The trigger wasn't created. Check the migration file and re-apply.

### Step 4: Test Trigger Manually

**Create a test listing to see if trigger fires:**
```sql
-- First, get a user ID
SELECT id FROM auth.users LIMIT 1;

-- Then create test listing (replace USER_ID with actual ID)
INSERT INTO business_listings (
  user_id,
  title,
  description,
  city,
  status
) VALUES (
  'USER_ID_HERE',
  'Test Listing ' || NOW(),
  'Test description',
  'Test City',
  'pending'
) RETURNING id, title, status;
```

**Check if email notification was created:**
```sql
SELECT * FROM email_notifications 
WHERE type = 'listing_approval' 
ORDER BY created_at DESC LIMIT 5;
```

### Step 5: Check Edge Functions

**Test if functions are deployed:**
```bash
# Test each function
curl -X POST "https://tcqltnxugtzludqhngrs.supabase.co/functions/v1/process-email-notifications" \
  -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY"

curl -X POST "https://tcqltnxugtzludqhngrs.supabase.co/functions/v1/send-listing-approval-email" \
  -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"listingId": "test"}'
```

**If 404 errors:** Functions not deployed. Run:
```bash
supabase functions deploy
```

### Step 6: Check Environment Variables

**In Supabase Dashboard:**
1. Go to Settings → Environment Variables
2. Verify `RESEND_API_KEY` is set
3. Check the key is valid in Resend dashboard

**Test Resend API key:**
```bash
curl -X POST "https://api.resend.com/emails" \
  -H "Authorization: Bearer YOUR_RESEND_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Test Email",
    "html": "<p>Test</p>"
  }'
```

### Step 7: Check Domain Verification

**In Resend Dashboard:**
1. Go to Domains
2. Verify `sa360.co.za` is verified
3. Check DNS records are properly set

### Step 8: Process Email Queue

**Check if emails are queued but not processed:**
```sql
SELECT COUNT(*) FROM email_notifications WHERE status = 'pending';
```

**If there are pending emails, process them:**
```bash
curl -X POST "https://tcqltnxugtzludqhngrs.supabase.co/functions/v1/process-email-notifications" \
  -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY"
```

## Common Issues and Solutions

### Issue 1: Migration Not Applied
**Symptoms:** Tables don't exist, trigger doesn't exist
**Solution:** 
```bash
supabase db push
```

### Issue 2: Trigger Not Firing
**Symptoms:** No email notifications in queue when creating listings
**Solutions:**
1. Check trigger exists and is enabled
2. Verify the trigger function has correct permissions
3. Check for any database errors in logs

### Issue 3: Functions Not Deployed
**Symptoms:** 404 errors when calling functions
**Solution:**
```bash
supabase functions deploy
```

### Issue 4: Resend API Issues
**Symptoms:** Emails queued but status shows 'failed'
**Solutions:**
1. Verify API key is correct
2. Check domain is verified in Resend
3. Verify sender email domain matches verified domain

### Issue 5: RLS Policies Blocking Access
**Symptoms:** Permission denied errors
**Solution:** Check RLS policies allow service role access:
```sql
SELECT * FROM pg_policies WHERE tablename = 'email_notifications';
```

### Issue 6: Wrong Email Address
**Symptoms:** Emails sent but not received
**Solution:** Verify the recipient email in the code:
- Check `process-email-notifications/index.ts`
- Look for `<EMAIL>`
- Verify this email address is correct and accessible

## Manual Testing

### Create Test Listing via API
```javascript
// In browser console on your site
const { data, error } = await supabase
  .from('business_listings')
  .insert({
    title: 'Test Listing',
    description: 'Test',
    city: 'Test City',
    status: 'pending'
  });
console.log(data, error);
```

### Check Email Queue
```javascript
// Check if email was queued
const { data, error } = await supabase
  .from('email_notifications')
  .select('*')
  .order('created_at', { ascending: false })
  .limit(5);
console.log(data, error);
```

### Process Emails Manually
```javascript
// Process pending emails
const { data, error } = await supabase.functions.invoke('process-email-notifications');
console.log(data, error);
```

## Getting Help

If you're still having issues:

1. **Run the debug script** and share the output
2. **Check Supabase logs** in the dashboard under Logs
3. **Verify your setup** matches the requirements in `LISTING_APPROVAL_SETUP.md`
4. **Test each component** individually using the manual tests above

## Quick Fix Checklist

- [ ] Migration applied (`supabase db push`)
- [ ] Functions deployed (`supabase functions deploy`)
- [ ] Resend API key set in environment variables
- [ ] Domain verified in Resend
- [ ] Tables exist (email_notifications, listing_approval_tokens)
- [ ] Trigger exists and enabled
- [ ] Test listing creates email notification
- [ ] Email processing function works
- [ ] Correct recipient email address

Run through this checklist and you should identify where the issue is!
