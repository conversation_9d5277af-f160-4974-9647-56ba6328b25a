
-- Create a storage bucket for business listing media
INSERT INTO storage.buckets (id, name, public)
VALUES ('business-media', 'business-media', true);

-- Create RLS policies for the business-media bucket
CREATE POLICY "Anyone can view business media files"
ON storage.objects FOR SELECT
USING (bucket_id = 'business-media');

CREATE POLICY "Authenticated users can upload business media files"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'business-media' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Users can update their own business media files"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'business-media' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own business media files"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'business-media' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);
