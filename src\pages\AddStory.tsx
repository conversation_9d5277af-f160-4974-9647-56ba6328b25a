import React, { useState } from "react";
import Layout from "@/components/layout/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { FileUpload } from "@/components/ui/file-upload";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";

const AddStory = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    businessName: "",
    title: "",
    excerpt: "",
    content: "",
    category: "",
    type: "article",
    isPremium: false,
    thumbnailUrl: "",
    agreeToTerms: false
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.agreeToTerms) {
      toast.error("You must agree to the terms and conditions");
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      toast.error("You must be logged in to submit a story");
      return;
    }

    try {
      const { error } = await supabase
        .from('business_stories')
        .insert({
          user_id: user.id,
          business_name: formData.businessName,
          title: formData.title,
          excerpt: formData.excerpt,
          content: formData.content,
          category: formData.category,
          type: formData.type,
          is_premium: formData.isPremium,
          thumbnail_url: formData.thumbnailUrl,
          status: 'pending'
        });

      if (error) throw error;

      toast.success("Story submitted successfully! We'll review it and get back to you soon.");
      setTimeout(() => navigate('/stories'), 2000);
    } catch (error) {
      console.error('Error submitting story:', error);
      toast.error("Failed to submit story. Please try again.");
    }
  };

  return (
    <Layout>
      <div className="py-8 bg-slate-50">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-6 text-center">Submit Your Business Story</h1>
          
          <form onSubmit={handleSubmit} className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Story Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  placeholder="Business Name"
                  value={formData.businessName}
                  onChange={(e) => setFormData({...formData, businessName: e.target.value})}
                  required
                />
                <Input
                  placeholder="Story Title"
                  value={formData.title}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  required
                />
                <Textarea
                  placeholder="Brief excerpt (2-3 sentences)"
                  value={formData.excerpt}
                  onChange={(e) => setFormData({...formData, excerpt: e.target.value})}
                  required
                />
                <Textarea
                  placeholder="Full story content"
                  value={formData.content}
                  onChange={(e) => setFormData({...formData, content: e.target.value})}
                  rows={10}
                  required
                />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Education">Education</SelectItem>
                      <SelectItem value="Food & Beverage">Food & Beverage</SelectItem>
                      <SelectItem value="Beauty & Wellness">Beauty & Wellness</SelectItem>
                      <SelectItem value="Retail">Retail</SelectItem>
                      <SelectItem value="Technology">Technology</SelectItem>
                      <SelectItem value="Mining & Resources">Mining & Resources</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Content Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="article">Article</SelectItem>
                      <SelectItem value="video">Video</SelectItem>
                      <SelectItem value="podcast">Podcast</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <FileUpload
                  onFileUpload={(url) => setFormData({...formData, thumbnailUrl: url})}
                  buttonText="Upload Thumbnail Image"
                  folder="story-thumbnails"
                />
              </CardContent>
            </Card>

            <div className="flex items-start space-x-2">
              <Checkbox
                checked={formData.agreeToTerms}
                onCheckedChange={(checked) => setFormData({...formData, agreeToTerms: checked as boolean})}
              />
              <label className="text-sm">
                I agree to the Terms & Conditions for posting this story.
              </label>
            </div>

            <div className="text-center">
              <Button type="submit" className="bg-[#007749] hover:bg-[#006739] px-8">
                Submit Story
              </Button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default AddStory;