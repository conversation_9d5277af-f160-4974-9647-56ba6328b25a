
import { useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export const useEmailNotifications = (autoProcess = false, intervalMs = 30000) => {
  // Function to manually trigger email processing
  const processEmailNotifications = useCallback(async () => {
    try {
      const { data, error } = await supabase.functions.invoke('process-email-notifications');
      
      if (error) {
        console.error('Error processing email notifications:', error);
        toast.error('Failed to process email notifications');
        return { success: false, error };
      }

      console.log('Email processing result:', data);
      
      if (data?.processed > 0) {
        toast.success(`Processed ${data.processed} email notifications`);
      }
      
      return { success: true, data };
    } catch (error) {
      console.error('Error calling email processor:', error);
      toast.error('Failed to process email notifications');
      return { success: false, error };
    }
  }, []);

  // Function to get pending email notifications count
  const getPendingNotificationsCount = useCallback(async () => {
    try {
      // Since we don't have an email_notifications table, we'll return 0 for now
      // In a real implementation, you would query the actual table
      console.log('Getting pending notifications count - table not implemented yet');
      return 0;
    } catch (error) {
      console.error('Error fetching pending notifications count:', error);
      return 0;
    }
  }, []);

  // Function to get email notifications with pagination
  const getEmailNotifications = useCallback(async (
    page = 0, 
    limit = 20, 
    status?: 'pending' | 'sent' | 'failed'
  ) => {
    try {
      // Since we don't have an email_notifications table, we'll return empty array for now
      console.log('Getting email notifications - table not implemented yet');
      return { data: [], error: null };
    } catch (error) {
      console.error('Error fetching email notifications:', error);
      return { data: [], error };
    }
  }, []);

  // Function to retry failed email notifications
  const retryFailedNotifications = useCallback(async () => {
    try {
      console.log('Retrying failed notifications - table not implemented yet');
      toast.info('Email notifications feature is not fully implemented yet');
      return { success: false, error: 'Feature not implemented' };
    } catch (error) {
      console.error('Error retrying failed notifications:', error);
      toast.error('Failed to retry notifications');
      return { success: false, error };
    }
  }, []);

  // Auto-process emails at regular intervals if enabled
  useEffect(() => {
    if (!autoProcess) return;

    const interval = setInterval(async () => {
      const pendingCount = await getPendingNotificationsCount();
      if (pendingCount > 0) {
        console.log(`Processing ${pendingCount} pending email notifications...`);
        await processEmailNotifications();
      }
    }, intervalMs);

    return () => clearInterval(interval);
  }, [autoProcess, intervalMs, processEmailNotifications, getPendingNotificationsCount]);

  return {
    processEmailNotifications,
    getPendingNotificationsCount,
    getEmailNotifications,
    retryFailedNotifications,
  };
};

// Utility function to manually trigger listing approval email
export const triggerListingApprovalEmail = async (listingId: string) => {
  try {
    const { data, error } = await supabase.functions.invoke('send-listing-approval-email', {
      body: { listingId }
    });

    if (error) {
      console.error('Error sending listing approval email:', error);
      toast.error('Failed to send approval email');
      return { success: false, error };
    }

    toast.success('Approval email sent successfully');
    return { success: true, data };
  } catch (error) {
    console.error('Error sending listing approval email:', error);
    toast.error('Failed to send approval email');
    return { success: false, error };
  }
};

// Hook for admin dashboard to monitor email notifications
export const useEmailNotificationStats = () => {
  const { getEmailNotifications, getPendingNotificationsCount } = useEmailNotifications();

  const getStats = useCallback(async () => {
    try {
      const pendingCount = await getPendingNotificationsCount();
      
      // Mock stats since we don't have the actual table yet
      return {
        pending: pendingCount,
        sent: 0,
        failed: 0,
        total: 0
      };
    } catch (error) {
      console.error('Error fetching email notification stats:', error);
      return {
        pending: 0,
        sent: 0,
        failed: 0,
        total: 0
      };
    }
  }, [getPendingNotificationsCount]);

  return { getStats };
};
