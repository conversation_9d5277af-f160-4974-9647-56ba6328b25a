-- Debug SQL queries for the listing approval email system
-- Run these queries in your Supabase SQL editor to diagnose issues

-- 1. Check if required tables exist
SELECT 
  schemaname, 
  tablename, 
  tableowner 
FROM pg_tables 
WHERE tablename IN ('business_listings', 'email_notifications', 'listing_approval_tokens')
ORDER BY tablename;

-- 2. Check if the trigger function exists
SELECT 
  proname as function_name,
  prosrc as function_source
FROM pg_proc 
WHERE proname = 'send_listing_approval_email';

-- 3. Check if the trigger exists and is enabled
SELECT 
  tgname as trigger_name,
  tgenabled as enabled,
  tgrelid::regclass as table_name
FROM pg_trigger 
WHERE tgname = 'trigger_send_listing_approval_email';

-- 4. Check recent business listings
SELECT 
  id,
  title,
  status,
  created_at,
  updated_at
FROM business_listings 
ORDER BY created_at DESC 
LIMIT 10;

-- 5. Check pending listings specifically
SELECT 
  id,
  title,
  status,
  created_at
FROM business_listings 
WHERE status = 'pending'
ORDER BY created_at DESC;

-- 6. Check email notifications queue
SELECT 
  id,
  type,
  recipient_email,
  subject,
  status,
  attempts,
  error_message,
  created_at,
  data
FROM email_notifications 
ORDE<PERSON> BY created_at DESC 
LIMIT 20;

-- 7. Check email notification status distribution
SELECT 
  status,
  COUNT(*) as count
FROM email_notifications 
WHERE type = 'listing_approval'
GROUP BY status;

-- 8. Check approval tokens
SELECT 
  id,
  listing_id,
  expires_at,
  used_at,
  created_at
FROM listing_approval_tokens 
ORDER BY created_at DESC 
LIMIT 10;

-- 9. Check for any failed email notifications with error details
SELECT 
  id,
  subject,
  attempts,
  error_message,
  last_attempt_at,
  created_at,
  data
FROM email_notifications 
WHERE status = 'failed' OR error_message IS NOT NULL
ORDER BY created_at DESC;

-- 10. Test the trigger function manually (if it exists)
-- This will show if the function works when called directly
-- DO NOT RUN THIS unless you want to create a test notification
/*
INSERT INTO email_notifications (
  type,
  recipient_email,
  subject,
  data,
  status
) VALUES (
  'test',
  '<EMAIL>',
  'Test Email',
  '{"test": true}',
  'pending'
);
*/

-- 11. Check if there are any constraints or policies blocking inserts
SELECT 
  conname as constraint_name,
  contype as constraint_type,
  conrelid::regclass as table_name
FROM pg_constraint 
WHERE conrelid IN (
  'email_notifications'::regclass,
  'business_listings'::regclass,
  'listing_approval_tokens'::regclass
);

-- 12. Check RLS policies
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE tablename IN ('email_notifications', 'listing_approval_tokens', 'business_listings');

-- 13. Check if the migration was applied correctly
SELECT 
  version,
  name,
  applied_at
FROM supabase_migrations.schema_migrations 
WHERE name LIKE '%listing-approval%' OR name LIKE '%email%'
ORDER BY applied_at DESC;

-- 14. Manual test: Create a test listing to see if trigger fires
-- UNCOMMENT AND RUN THIS ONLY IF YOU WANT TO CREATE A TEST LISTING
/*
INSERT INTO business_listings (
  user_id,
  title,
  description,
  city,
  status
) VALUES (
  (SELECT id FROM auth.users LIMIT 1), -- Uses first available user
  'Manual Test Listing ' || NOW(),
  'This is a manual test to check if the trigger fires',
  'Test City',
  'pending'
) RETURNING id, title, status;
*/

-- 15. Check recent database logs for any trigger errors
-- Note: This might not work depending on your Supabase plan
SELECT 
  log_time,
  message
FROM pg_log 
WHERE message LIKE '%send_listing_approval_email%' 
   OR message LIKE '%trigger_send_listing_approval_email%'
ORDER BY log_time DESC 
LIMIT 10;
