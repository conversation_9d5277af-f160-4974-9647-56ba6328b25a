import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import SEOHead from "@/components/seo/SEOHead";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Star, 
  MapPin, 
  Phone, 
  Globe, 
  Clock, 
  Share2, 
  Heart,
  ChevronLeft,
  ChevronRight,
  Verified,
  ExternalLink
} from "lucide-react";
import { useBusinessDetail } from "@/hooks/useBusinessDetail";
import PremiumBadge from "@/components/business/PremiumBadge";
import ReviewsList from "@/components/reviews/ReviewsList";

const BusinessDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  
  const { data: business, isLoading, error } = useBusinessDetail(id || '');

  if (isLoading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-200 rounded w-1/3 mb-4"></div>
            <div className="h-64 bg-slate-200 rounded mb-6"></div>
            <div className="space-y-4">
              <div className="h-6 bg-slate-200 rounded w-1/2"></div>
              <div className="h-4 bg-slate-200 rounded w-3/4"></div>
              <div className="h-4 bg-slate-200 rounded w-2/3"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !business) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-slate-800 mb-4">Business Not Found</h1>
            <p className="text-slate-600 mb-6">
              The business you're looking for doesn't exist or has been removed.
            </p>
            <Link to="/businesses">
              <Button>Browse All Businesses</Button>
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  const isPremium = business.subscription_tier && business.subscription_tier !== 'free';
  const images = business.images || [];
  const hasMultipleImages = images.length > 1;
  const currentImage = images[currentImageIndex] || business.logo_url || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80';

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": business.title,
    "description": business.description,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": business.full_address,
      "addressLocality": business.city,
      "addressCountry": "ZA"
    },
    "telephone": business.phone,
    "url": business.website,
    "image": currentImage,
    "priceRange": business.price_range
  };

  return (
    <>
      <SEOHead
        title={`${business.title} - SA360 Business Directory`}
        description={business.description || `Find ${business.title} in ${business.city}. Contact details, reviews, and more on SA360.`}
        keywords={`${business.title}, ${business.categories?.name}, ${business.city}, South Africa business`}
        structuredData={structuredData}
      />
      <Layout>
        <div className="container mx-auto px-4 py-8">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm text-slate-600 mb-6">
            <Link to="/" className="hover:text-[#007749]">Home</Link>
            <span>/</span>
            <Link to="/businesses" className="hover:text-[#007749]">Businesses</Link>
            {business.categories && (
              <>
                <span>/</span>
                <Link to={`/category/${business.category_id}`} className="hover:text-[#007749]">
                  {business.categories.name}
                </Link>
              </>
            )}
            <span>/</span>
            <span className="text-slate-800">{business.title}</span>
          </nav>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Image Gallery */}
              <div className="relative mb-6">
                <div className="aspect-[16/9] relative rounded-lg overflow-hidden">
                  <img 
                    src={currentImage} 
                    alt={business.title}
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Image Navigation */}
                  {hasMultipleImages && (
                    <>
                      <button
                        onClick={prevImage}
                        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                      >
                        <ChevronLeft className="h-5 w-5" />
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                      >
                        <ChevronRight className="h-5 w-5" />
                      </button>
                      
                      {/* Image Counter */}
                      <div className="absolute bottom-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-sm">
                        {currentImageIndex + 1} / {images.length}
                      </div>
                    </>
                  )}

                  {/* Badges */}
                  <div className="absolute top-4 right-4 flex gap-2">
                    {isPremium && (
                      <PremiumBadge tier={business.subscription_tier} />
                    )}
                    {business.featured && (
                      <Badge className="bg-[#FDB913] text-black">
                        Featured
                      </Badge>
                    )}
                    {business.verified && (
                      <Badge className="bg-green-600 text-white flex items-center gap-1">
                        <Verified className="h-3 w-3" />
                        Verified
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Thumbnail Strip */}
                {hasMultipleImages && (
                  <div className="flex gap-2 mt-4 overflow-x-auto">
                    {images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                          index === currentImageIndex ? 'border-[#007749]' : 'border-transparent'
                        }`}
                      >
                        <img 
                          src={image} 
                          alt={`${business.title} ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Business Info */}
              <div className="mb-8">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h1 className="text-3xl font-bold text-slate-800 mb-2">{business.title}</h1>
                    {business.categories && (
                      <p className="text-lg text-slate-600">{business.categories.name}</p>
                    )}
                  </div>
                  
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Heart className="h-4 w-4 mr-2" />
                      Save
                    </Button>
                    <Button variant="outline" size="sm">
                      <Share2 className="h-4 w-4 mr-2" />
                      Share
                    </Button>
                  </div>
                </div>

                {/* Rating */}
                <div className="flex items-center mb-4">
                  <div className="flex items-center text-amber-500">
                    <Star className="h-5 w-5 fill-current" />
                    <span className="ml-1 text-lg font-medium">4.5</span>
                  </div>
                  <span className="mx-2 text-slate-300">•</span>
                  <span className="text-slate-600">Reviews</span>
                </div>

                {/* Description */}
                {business.description && (
                  <div className="mb-6">
                    <h2 className="text-xl font-semibold text-slate-800 mb-3">About</h2>
                    <p className="text-slate-700 leading-relaxed">{business.description}</p>
                  </div>
                )}
              </div>

              {/* Reviews Section */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-slate-800 mb-4">Reviews</h2>
                <ReviewsList businessId={business.id} />
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Card className="sticky top-4">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-slate-800 mb-4">Contact Information</h3>
                  
                  <div className="space-y-4">
                    {business.full_address && (
                      <div className="flex items-start">
                        <MapPin className="h-5 w-5 text-slate-500 mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-slate-700">{business.full_address}</span>
                      </div>
                    )}
                    
                    {business.phone && (
                      <div className="flex items-center">
                        <Phone className="h-5 w-5 text-slate-500 mr-3 flex-shrink-0" />
                        <a 
                          href={`tel:${business.phone}`}
                          className="text-slate-700 hover:text-[#007749]"
                        >
                          {business.phone}
                        </a>
                      </div>
                    )}
                    
                    {business.website && (
                      <div className="flex items-center">
                        <Globe className="h-5 w-5 text-slate-500 mr-3 flex-shrink-0" />
                        <a 
                          href={business.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-slate-700 hover:text-[#007749] flex items-center"
                        >
                          Visit Website
                          <ExternalLink className="h-4 w-4 ml-1" />
                        </a>
                      </div>
                    )}

                    {business.price_range && (
                      <div className="pt-4 border-t">
                        <span className="text-sm text-slate-500">Price Range</span>
                        <p className="font-medium text-slate-800">{business.price_range}</p>
                      </div>
                    )}
                  </div>

                  <div className="mt-6 space-y-3">
                    <Button className="w-full bg-[#007749] hover:bg-[#005a37]">
                      Contact Business
                    </Button>
                    <Button variant="outline" className="w-full">
                      Get Directions
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default BusinessDetail;
