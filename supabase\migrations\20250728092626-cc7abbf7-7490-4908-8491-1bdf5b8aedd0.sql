
-- Fix the notify_new_listing function to not use the non-existent net schema
-- We'll create a simpler version that doesn't try to call the edge function
CREATE OR REPLACE FUNCTION public.notify_new_listing()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
BEGIN
  -- Only send notification for new pending listings
  IF NEW.status = 'pending' AND (TG_OP = 'INSERT' OR OLD.status != 'pending') THEN
    -- For now, just log that a new listing was created
    -- In the future, this could be enhanced to send notifications
    RAISE NOTICE 'New listing created: % with ID: %', NEW.title, NEW.id;
  END IF;
  
  RETURN NEW;
END;
$function$;
