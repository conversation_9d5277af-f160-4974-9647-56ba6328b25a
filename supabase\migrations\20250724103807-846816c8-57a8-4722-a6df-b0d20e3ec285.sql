
-- Create business_listing_translations table
CREATE TABLE public.business_listing_translations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id UUID NOT NULL REFERENCES business_listings(id) ON DELETE CASCADE,
  language_code TEXT NOT NULL,
  title TEXT,
  description TEXT,
  translated_by TEXT NOT NULL,
  translation_quality DECIMAL DEFAULT 1.0,
  is_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(business_id, language_code)
);

-- Create business_reviews table
CREATE TABLE public.business_reviews (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id UUID NOT NULL REFERENCES business_listings(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  review_title TEXT,
  review_text TEXT,
  verified_purchase BOOLEAN DEFAULT false,
  recommended BOOLEAN DEFAULT true,
  visit_date DATE,
  images TEXT[],
  status TEXT NOT NULL DEFAULT 'published',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(business_id, user_id)
);

-- Add RLS policies for business_listing_translations
ALTER TABLE public.business_listing_translations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view translations for approved listings" 
  ON public.business_listing_translations 
  FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM business_listings 
    WHERE business_listings.id = business_listing_translations.business_id 
    AND (business_listings.status = 'approved' OR business_listings.user_id = auth.uid())
  ));

CREATE POLICY "Users can manage translations for their own listings" 
  ON public.business_listing_translations 
  FOR ALL 
  USING (EXISTS (
    SELECT 1 FROM business_listings 
    WHERE business_listings.id = business_listing_translations.business_id 
    AND business_listings.user_id = auth.uid()
  ));

-- Add RLS policies for business_reviews
ALTER TABLE public.business_reviews ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view published reviews" 
  ON public.business_reviews 
  FOR SELECT 
  USING (status = 'published');

CREATE POLICY "Users can create reviews" 
  ON public.business_reviews 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reviews" 
  ON public.business_reviews 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reviews" 
  ON public.business_reviews 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Add triggers for updated_at
CREATE TRIGGER update_business_listing_translations_updated_at 
  BEFORE UPDATE ON public.business_listing_translations 
  FOR EACH ROW 
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_business_reviews_updated_at 
  BEFORE UPDATE ON public.business_reviews 
  FOR EACH ROW 
  EXECUTE FUNCTION public.update_updated_at_column();
