
-- Create a function to send approval emails when new listings are submitted
CREATE OR REPLACE FUNCTION notify_new_listing()
RETURNS TRIGGER AS $$
BEGIN
  -- Only send notification for new pending listings
  IF NEW.status = 'pending' AND (TG_OP = 'INSERT' OR OLD.status != 'pending') THEN
    -- Call the edge function to send email notification
    PERFORM
      net.http_post(
        url := 'https://tcqltnxugtzludqhngrs.supabase.co/functions/v1/send-listing-approval-email',
        headers := jsonb_build_object(
          'Content-Type', 'application/json',
          'Authorization', 'Bearer ' || current_setting('app.jwt_secret', true)
        ),
        body := jsonb_build_object(
          'listing_id', NEW.id,
          'title', NEW.title,
          'description', NEW.description,
          'city', NEW.city,
          'user_id', NEW.user_id,
          'created_at', NEW.created_at
        )
      );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate trigger to send approval emails for new listings
DROP TRIGGER IF EXISTS trigger_notify_new_listing ON business_listings;
CREATE TRIGGER trigger_notify_new_listing
  AFTER INSERT OR UPDATE OF status ON business_listings
  FOR EACH ROW
  EXECUTE FUNCTION notify_new_listing();

-- Create a function to approve listings via token
CREATE OR REPLACE FUNCTION approve_listing_by_token(listing_token text)
RETURNS jsonb AS $$
DECLARE
  listing_id uuid;
  result jsonb;
BEGIN
  -- Extract listing ID from token (simple base64 decode for demo)
  -- In production, use proper JWT or encrypted tokens
  listing_id := listing_token::uuid;
  
  -- Update the listing status to approved
  UPDATE business_listings 
  SET status = 'approved', updated_at = now()
  WHERE id = listing_id AND status = 'pending';
  
  -- Check if update was successful
  IF FOUND THEN
    result := jsonb_build_object(
      'success', true,
      'message', 'Listing approved successfully',
      'listing_id', listing_id
    );
  ELSE
    result := jsonb_build_object(
      'success', false,
      'message', 'Listing not found or already processed'
    );
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;
