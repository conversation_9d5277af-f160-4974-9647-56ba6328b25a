-- Create table to store listing approval tokens
CREATE TABLE public.listing_approval_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  listing_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE,
  token TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create table to store email notifications queue
CREATE TABLE public.email_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type TEXT NOT NULL,
  recipient_email TEXT NOT NULL,
  subject TEXT NOT NULL,
  data JSONB,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed')),
  attempts INTEGER DEFAULT 0,
  last_attempt_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> indexes for faster lookups
CREATE INDEX idx_listing_approval_tokens_token ON public.listing_approval_tokens(token);
CREATE INDEX idx_listing_approval_tokens_listing_id ON public.listing_approval_tokens(listing_id);
CREATE INDEX idx_listing_approval_tokens_expires_at ON public.listing_approval_tokens(expires_at);
CREATE INDEX idx_email_notifications_status ON public.email_notifications(status);
CREATE INDEX idx_email_notifications_type ON public.email_notifications(type);
CREATE INDEX idx_email_notifications_created_at ON public.email_notifications(created_at);

-- Function to send listing approval email
-- This function creates a notification record that can be processed by a background job
CREATE OR REPLACE FUNCTION public.send_listing_approval_email()
RETURNS TRIGGER AS $$
BEGIN
  -- Only send email for new pending listings
  IF NEW.status = 'pending' AND (OLD IS NULL OR OLD.status != 'pending') THEN
    -- Insert a notification record that will be processed by the application
    INSERT INTO public.email_notifications (
      type,
      recipient_email,
      subject,
      data,
      status
    ) VALUES (
      'listing_approval',
      '<EMAIL>',
      'New Listing Approval Required: ' || NEW.title,
      jsonb_build_object(
        'listingId', NEW.id,
        'listingTitle', NEW.title,
        'submittedAt', NEW.created_at
      ),
      'pending'
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically send approval emails
CREATE TRIGGER trigger_send_listing_approval_email
  AFTER INSERT OR UPDATE ON public.business_listings
  FOR EACH ROW
  EXECUTE FUNCTION public.send_listing_approval_email();

-- Function to clean up expired tokens (can be called periodically)
CREATE OR REPLACE FUNCTION public.cleanup_expired_approval_tokens()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM public.listing_approval_tokens 
  WHERE expires_at < NOW();
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.listing_approval_tokens TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.email_notifications TO authenticated;
GRANT EXECUTE ON FUNCTION public.cleanup_expired_approval_tokens() TO authenticated;

-- Enable RLS on the tables
ALTER TABLE public.listing_approval_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Allow service role full access
CREATE POLICY "Service role can manage approval tokens" ON public.listing_approval_tokens
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage email notifications" ON public.email_notifications
  FOR ALL USING (auth.role() = 'service_role');

-- Allow authenticated users to insert email notifications (for trigger function)
-- The trigger function runs as SECURITY DEFINER but may still need this policy
CREATE POLICY "Allow authenticated to insert email notifications" ON public.email_notifications
  FOR INSERT TO authenticated WITH CHECK (true);

-- Add comment for documentation
COMMENT ON TABLE public.listing_approval_tokens IS 'Stores temporary tokens for listing approval via email links';
COMMENT ON FUNCTION public.send_listing_approval_email() IS 'Automatically sends approval email when a listing is submitted';
COMMENT ON FUNCTION public.cleanup_expired_approval_tokens() IS 'Removes expired approval tokens from the database';
