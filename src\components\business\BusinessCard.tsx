
import React from 'react';
import { Link } from 'react-router-dom';
import { Star, MapPin, Phone, Globe, Verified } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import PremiumBadge from './PremiumBadge';
import { BusinessListing } from '@/hooks/useBusinessListings';

interface BusinessCardProps {
  business: BusinessListing;
  featured?: boolean;
}

const BusinessCard = ({ business, featured = false }: BusinessCardProps) => {
  const isPremium = business.subscription_tier && business.subscription_tier !== 'free';
  const hasMultipleImages = business.images && business.images.length > 1;
  
  return (
    <Card className={`group overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 bg-white ${
      isPremium ? 'ring-2 ring-[#FDB913] ring-opacity-50 shadow-lg' : 'shadow-sm'
    } ${featured ? 'border-[#007749] shadow-lg ring-2 ring-[#007749] ring-opacity-30' : 'border-slate-200'}`}>
      <div className="aspect-[16/9] relative overflow-hidden">
        <img
          src={business.images?.[0] || business.logo_url || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80'}
          alt={business.title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        />

        {/* Gradient overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <div className="absolute top-3 right-3 flex flex-col gap-2">
          {business.subscription_tier && business.subscription_tier !== 'free' && (
            <PremiumBadge tier={business.subscription_tier} />
          )}
          {business.featured && (
            <Badge className="bg-[#FDB913] text-black font-semibold shadow-lg">
              ⭐ Featured
            </Badge>
          )}
          {business.verified && (
            <Badge className="bg-green-600 text-white flex items-center gap-1 shadow-lg">
              <Verified className="h-3 w-3" />
              Verified
            </Badge>
          )}
        </div>

        {hasMultipleImages && (
          <div className="absolute bottom-3 right-3">
            <Badge variant="secondary" className="text-xs bg-black/70 text-white border-0">
              📷 +{business.images!.length - 1} photos
            </Badge>
          </div>
        )}

        {/* Quick action overlay */}
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <Link to={`/business/${business.id}`}>
            <Badge className="bg-white text-slate-800 hover:bg-slate-100 px-4 py-2 text-sm font-medium">
              View Details →
            </Badge>
          </Link>
        </div>
      </div>

      <CardContent className="p-5">
        <div className="mb-3">
          <Link to={`/business/${business.id}`} className="block">
            <h3 className={`font-bold text-xl text-slate-800 hover:text-[#007749] transition-colors line-clamp-2 leading-tight ${
              isPremium ? 'text-[#003087]' : ''
            } ${featured ? 'text-[#007749]' : ''}`}>
              {business.title}
            </h3>
          </Link>
          {business.categories && (
            <p className="text-sm text-slate-500 font-medium mt-1">
              {business.categories.name}
            </p>
          )}
        </div>

        <div className="space-y-2 mb-4">
          {business.full_address && (
            <div className="flex items-start text-sm text-slate-600">
              <MapPin className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5 text-slate-400" />
              <span className="line-clamp-1">{business.full_address}</span>
            </div>
          )}

          {business.phone && (
            <div className="flex items-center text-sm text-slate-600">
              <Phone className="h-4 w-4 mr-2 flex-shrink-0 text-slate-400" />
              <span className="font-medium">{business.phone}</span>
            </div>
          )}

          {business.website && (
            <div className="flex items-center text-sm">
              <Globe className="h-4 w-4 mr-2 flex-shrink-0 text-slate-400" />
              <a
                href={business.website}
                target="_blank"
                rel="noopener noreferrer"
                className="text-[#007749] hover:text-[#005a37] font-medium hover:underline truncate transition-colors"
                onClick={(e) => e.stopPropagation()}
              >
                Visit Website
              </a>
            </div>
          )}
        </div>

        {business.description && (
          <p className="text-sm text-slate-600 mb-4 line-clamp-2 leading-relaxed">
            {business.description}
          </p>
        )}

        <div className="flex items-center justify-between pt-3 border-t border-slate-100">
          <div className="flex items-center">
            <div className="flex items-center text-amber-500">
              <Star className="h-4 w-4 fill-current" />
              <span className="ml-1 text-sm font-bold">4.5</span>
            </div>
            <span className="mx-2 text-slate-300">•</span>
            <span className="text-sm text-slate-500 font-medium">24 Reviews</span>
          </div>

          {business.price_range && (
            <Badge variant="outline" className="text-xs font-semibold border-[#007749] text-[#007749]">
              {business.price_range}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default BusinessCard;
