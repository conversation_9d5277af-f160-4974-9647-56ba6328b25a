
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { AuthProvider } from "@/contexts/AuthContext";
import { LanguageProvider } from "@/contexts/LanguageContext";
import Index from "./pages/Index";
import Businesses from "./pages/Businesses";
import Categories from "./pages/Categories";
import CategoryDetail from "./pages/CategoryDetail";
import BusinessDetail from "./pages/BusinessDetail";
import AddListing from "./pages/AddListing";
import Pricing from "./pages/Pricing";
import SubscriptionSuccess from "./pages/SubscriptionSuccess";
import Auth from "./pages/Auth";
import SignIn from "./pages/SignIn";
import NotFound from "./pages/NotFound";
import Blog from "./pages/Blog";
import BlogArticle from "./pages/BlogArticle";
import BusinessMatch from "./pages/BusinessMatch";
import BusinessStories from "./pages/BusinessStories";
import SpecialOffers from "./pages/SpecialOffers";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import TestSeed from "./pages/TestSeed";
import BusinessesSimple from "./pages/BusinessesSimple";
import Services from "./pages/Services";
import Events from "./pages/Events";
import Advertising from "./pages/Advertising";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <HelmetProvider>
      <AuthProvider>
        <LanguageProvider>
          <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/businesses" element={<Businesses />} />
              <Route path="/business/:id" element={<BusinessDetail />} />
              <Route path="/categories" element={<Categories />} />
              <Route path="/category/:categoryId" element={<CategoryDetail />} />
              <Route path="/pricing" element={<Pricing />} />
              <Route path="/subscription-success" element={<SubscriptionSuccess />} />
              <Route path="/auth" element={<Auth />} />
              <Route path="/signin" element={<SignIn />} />
              <Route path="/blog" element={<Blog />} />
              <Route path="/blog/:slug" element={<BlogArticle />} />
              <Route path="/business-match" element={<BusinessMatch />} />
              <Route path="/business-stories" element={<BusinessStories />} />
              <Route path="/stories" element={<BusinessStories />} />
              <Route path="/special-offers" element={<SpecialOffers />} />
              <Route
                path="/add-listing"
                element={
                  <ProtectedRoute>
                    <AddListing />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/submit-listing"
                element={
                  <ProtectedRoute>
                    <AddListing />
                  </ProtectedRoute>
                }
              />
              <Route path="/test-seed" element={<TestSeed />} />
              <Route path="/businesses-simple" element={<BusinessesSimple />} />
              <Route path="/services" element={<Services />} />
              <Route path="/events" element={<Events />} />
              <Route path="/advertising" element={<Advertising />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
          </TooltipProvider>
        </LanguageProvider>
      </AuthProvider>
    </HelmetProvider>
  </QueryClientProvider>
);

export default App;
