
import React, { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { MapPin, Star, Heart, Bell } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";

interface BusinessMatch {
  id: string;
  title: string;
  description: string | null;
  city: string | null;
  category_id: string | null;
  featured: boolean | null;
  verified: boolean | null;
  subscription_tier: string | null;
  images: string[] | null;
  logo_url: string | null;
  website: string | null;
  categories?: {
    name: string;
    slug: string;
  };
  matchScore: number;
}

type BusinessMatchResultsProps = {
  answers: Record<string, string>;
};

const BusinessMatchResults = ({ answers }: BusinessMatchResultsProps) => {
  const { user } = useAuth();
  const [businesses, setBusinesses] = useState<BusinessMatch[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [savedBusinesses, setSavedBusinesses] = useState<Set<string>>(new Set());

  useEffect(() => {
    findMatches();
  }, [answers]);

  const findMatches = async () => {
    try {
      // Get all approved businesses with categories
      const { data: businessData, error } = await supabase
        .from('business_listings')
        .select(`
          *,
          categories (
            name,
            slug
          )
        `)
        .eq('status', 'approved')
        .limit(50);

      if (error) throw error;

      // Calculate match scores using a simple algorithm
      const matchedBusinesses: BusinessMatch[] = [];

      for (const business of businessData || []) {
        try {
          // Simple matching algorithm based on location, category, and description
          let matchScore = 0;
          
          // Location matching
          if (answers.location && business.city) {
            if (business.city.toLowerCase().includes(answers.location.toLowerCase())) {
              matchScore += 30;
            }
          }
          
          // Category matching
          if (answers.category && business.categories) {
            if (business.categories.name.toLowerCase().includes(answers.category.toLowerCase())) {
              matchScore += 40;
            }
          }
          
          // Description matching
          if (answers.description && business.description) {
            const descriptionWords = answers.description.toLowerCase().split(' ');
            const businessDesc = business.description.toLowerCase();
            const matchingWords = descriptionWords.filter(word => 
              businessDesc.includes(word) && word.length > 3
            );
            matchScore += Math.min(matchingWords.length * 5, 30);
          }
          
          // Add base score for all businesses
          matchScore += 20;
          
          // Boost for premium businesses
          if (business.featured || business.subscription_tier === 'premium') {
            matchScore += 10;
          }

          // Only include businesses with a reasonable match score
          if (matchScore >= 30) {
            matchedBusinesses.push({
              ...business,
              matchScore: Math.min(matchScore, 100)
            });
          }
        } catch (error) {
          console.error('Error processing business:', business.id, error);
        }
      }

      // Sort by match score (highest first)
      matchedBusinesses.sort((a, b) => b.matchScore - a.matchScore);

      // Take top 10 matches
      setBusinesses(matchedBusinesses.slice(0, 10));

    } catch (error) {
      console.error('Error finding matches:', error);
      toast.error('Failed to find matches. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-12 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6 text-slate-800">Finding Your Perfect Match</h2>
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="h-8 w-8 rounded-full border-4 border-t-[#007749] border-r-transparent border-b-transparent border-l-transparent animate-spin"></div>
            <p className="text-slate-600">Our AI is analyzing your preferences...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl font-bold mb-6 text-slate-800">Your Personalized Business Matches</h2>
        <p className="text-slate-600 mb-8">
          Based on your preferences, we've found these businesses that match your needs.
        </p>

        <div className="space-y-6">
          {businesses.map((business) => (
            <Card key={business.id} className="overflow-hidden">
              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/3 h-48 md:h-auto">
                  <img
                    src={business.images?.[0] || business.logo_url || "https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80"}
                    alt={business.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardContent className="md:w-2/3 p-6">
                  <div className="flex flex-col md:flex-row md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-1">{business.title}</h3>
                      <p className="text-sm text-slate-500 mb-2">
                        {business.categories?.name || 'Business'} • <MapPin className="inline h-4 w-4" /> {business.city || 'Location not specified'}
                      </p>
                    </div>
                    <div className="bg-[#007749] text-white px-3 py-1 rounded-lg flex items-center mt-2 md:mt-0">
                      <span className="font-medium">{business.matchScore}% Match</span>
                    </div>
                  </div>
                  
                  <p className="text-slate-600 my-3">{business.description || 'No description available'}</p>
                  
                  <div className="flex items-center mt-4">
                    <div className="flex items-center text-amber-500">
                      <Star className="h-4 w-4 fill-current" />
                      <span className="ml-1 text-sm font-medium">4.5</span>
                    </div>
                    <span className="mx-2 text-slate-300">•</span>
                    <span className="text-sm text-slate-500">48 reviews</span>
                    
                    {(business.featured || business.subscription_tier === 'premium') && (
                      <>
                        <span className="mx-2 text-slate-300">•</span>
                        <Badge className="bg-[#FDB913] text-black">Premium</Badge>
                      </>
                    )}
                  </div>
                  
                  <div className="mt-4 pt-4 border-t border-slate-200">
                    <Button asChild className="bg-[#007749] hover:bg-[#006739]">
                      <Link to={`/business/${business.id}`}>View Business</Link>
                    </Button>
                  </div>
                </CardContent>
              </div>
            </Card>
          ))}
        </div>
        
        <div className="mt-10 text-center">
          <Button variant="outline" asChild>
            <Link to="/business-match">Start Over</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BusinessMatchResults;
