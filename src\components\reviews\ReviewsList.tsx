
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Star, ThumbsUp, MessageSquare, Shield } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

interface Review {
  id: string;
  business_id: string;
  user_id: string;
  rating: number;
  review_title: string;
  review_text: string;
  images: string[];
  verified_purchase: boolean;
  visit_date: string;
  status: string;
  recommended: boolean | null;
  created_at: string;
  updated_at: string;
  // Extended fields for functionality
  helpful_votes: number;
  total_votes: number;
  response_from_business: string | null;
  response_date: string | null;
}

interface BusinessReputation {
  average_rating: number;
  total_reviews: number;
  verified_reviews: number;
  recommended_percentage: number;
  response_rate: number;
}

interface ReviewsListProps {
  businessId: string;
  showBusinessOwnerFeatures?: boolean;
}

const ReviewsList: React.FC<ReviewsListProps> = ({ businessId, showBusinessOwnerFeatures = false }) => {
  const { user } = useAuth();
  const [responseText, setResponseText] = useState<{ [key: string]: string }>({});

  const { data: reviews, isLoading: reviewsLoading, refetch } = useQuery({
    queryKey: ['business-reviews', businessId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('business_reviews')
        .select('*')
        .eq('business_id', businessId)
        .eq('status', 'published')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      // Transform the data to match our Review interface with default values
      return data?.map(review => ({
        ...review,
        helpful_votes: 0, // Default value since column may not exist yet
        total_votes: 0,   // Default value since column may not exist yet
        response_from_business: null, // Default value since column may not exist yet
        response_date: null          // Default value since column may not exist yet
      })) as Review[];
    },
  });

  const { data: reputation } = useQuery({
    queryKey: ['business-reputation', businessId],
    queryFn: async () => {
      // Calculate basic stats from reviews since reputation table may not exist yet
      const { data: reviewsData, error } = await supabase
        .from('business_reviews')
        .select('rating, verified_purchase, recommended')
        .eq('business_id', businessId)
        .eq('status', 'published');
        
      if (error) {
        console.log('Reviews query error:', error);
        return null;
      }
      
      if (reviewsData && reviewsData.length > 0) {
        const totalReviews = reviewsData.length;
        const averageRating = reviewsData.reduce((sum, r) => sum + r.rating, 0) / totalReviews;
        const verifiedReviews = reviewsData.filter(r => r.verified_purchase).length;
        const recommendedReviews = reviewsData.filter(r => r.recommended).length;
        const recommendedPercentage = (recommendedReviews / totalReviews) * 100;
        
        return {
          average_rating: averageRating,
          total_reviews: totalReviews,
          verified_reviews: verifiedReviews,
          recommended_percentage: recommendedPercentage,
          response_rate: 0
        } as BusinessReputation;
      }
      
      return null;
    },
  });

  const handleVote = async (reviewId: string, voteType: 'helpful' | 'not_helpful') => {
    if (!user) {
      toast.error('Please sign in to vote on reviews');
      return;
    }

    try {
      // For now, just show a message since vote functionality requires additional tables
      toast.info('Vote functionality will be available once the review system is fully set up');
      
    } catch (error) {
      console.error('Error voting on review:', error);
      toast.error('Failed to submit vote');
    }
  };

  const handleBusinessResponse = async (reviewId: string) => {
    const response = responseText[reviewId];
    if (!response?.trim()) {
      toast.error('Please enter a response');
      return;
    }

    try {
      // For now, just show a message since response functionality requires additional columns
      toast.info('Business response functionality will be available once the review system is fully set up');
      
    } catch (error) {
      console.error('Error submitting response:', error);
      toast.error('Failed to submit response');
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  if (reviewsLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#007749]"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Business Reputation Summary */}
      {reputation && (
        <Card>
          <CardHeader>
            <CardTitle>Customer Reviews Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-[#007749]">
                  {reputation.average_rating?.toFixed(1) || '0.0'}
                </div>
                <div className="text-sm text-gray-600">Average Rating</div>
              </div>
              
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {reputation.total_reviews || 0}
                </div>
                <div className="text-sm text-gray-600">Total Reviews</div>
              </div>
              
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {reputation.recommended_percentage?.toFixed(0) || 0}%
                </div>
                <div className="text-sm text-gray-600">Recommended</div>
              </div>
              
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {reputation.verified_reviews || 0}
                </div>
                <div className="text-sm text-gray-600">Verified Reviews</div>
              </div>
              
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {reputation.response_rate?.toFixed(0) || 0}%
                </div>
                <div className="text-sm text-gray-600">Response Rate</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews && reviews.length > 0 ? (
          reviews.map((review) => (
            <Card key={review.id}>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Review Header */}
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center">
                          {renderStars(review.rating)}
                        </div>
                        <span className="font-semibold">{review.rating}/5</span>
                        {review.verified_purchase && (
                          <Badge className="bg-green-100 text-green-800">
                            <Shield className="h-3 w-3 mr-1" />
                            Verified
                          </Badge>
                        )}
                      </div>
                      <h3 className="font-semibold text-lg">{review.review_title}</h3>
                      <div className="text-sm text-gray-500">
                        Visit Date: {new Date(review.visit_date).toLocaleDateString()}
                      </div>
                    </div>
                    
                    <div className="text-sm text-gray-500">
                      {new Date(review.created_at).toLocaleDateString()}
                    </div>
                  </div>

                  {/* Review Content */}
                  <p className="text-gray-700">{review.review_text}</p>

                  {/* Recommendation */}
                  {review.recommended !== null && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        {review.recommended ? '👍 Recommends this business' : '👎 Does not recommend this business'}
                      </span>
                    </div>
                  )}

                  {/* Review Images */}
                  {review.images && review.images.length > 0 && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {review.images.map((image, index) => (
                        <img
                          key={index}
                          src={image}
                          alt={`Review image ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                      ))}
                    </div>
                  )}

                  {/* Review Actions */}
                  <div className="flex items-center gap-4 pt-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleVote(review.id, 'helpful')}
                      className="flex items-center gap-1"
                    >
                      <ThumbsUp className="h-4 w-4" />
                      Helpful ({review.helpful_votes})
                    </Button>
                    
                    {showBusinessOwnerFeatures && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                      >
                        <MessageSquare className="h-4 w-4" />
                        Respond
                      </Button>
                    )}
                  </div>

                  {/* Business Response */}
                  {review.response_from_business && (
                    <div className="bg-gray-50 p-4 rounded-lg border-l-4 border-[#007749]">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="font-semibold text-[#007749]">Business Response</div>
                        <div className="text-sm text-gray-500">
                          {review.response_date && new Date(review.response_date).toLocaleDateString()}
                        </div>
                      </div>
                      <p className="text-gray-700">{review.response_from_business}</p>
                    </div>
                  )}

                  {/* Response Form for Business Owners */}
                  {showBusinessOwnerFeatures && !review.response_from_business && (
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="space-y-3">
                        <label className="block text-sm font-medium">Respond to this review:</label>
                        <textarea
                          className="w-full p-3 border rounded-lg resize-none"
                          rows={3}
                          placeholder="Write your response..."
                          value={responseText[review.id] || ''}
                          onChange={(e) => setResponseText(prev => ({
                            ...prev,
                            [review.id]: e.target.value
                          }))}
                        />
                        <Button
                          onClick={() => handleBusinessResponse(review.id)}
                          className="bg-[#007749] hover:bg-[#006739]"
                        >
                          Post Response
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-gray-500">No reviews yet. Be the first to leave a review!</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ReviewsList;
