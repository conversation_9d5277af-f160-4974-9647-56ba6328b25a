import React, { useState } from "react";
import Layout from "@/components/layout/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useQuery } from "@tanstack/react-query";

const AddSpecialOffer = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState({
    businessId: "",
    title: "",
    description: "",
    discount: "",
    category: "",
    location: "",
    startDate: null as Date | null,
    endDate: null as Date | null,
    imageUrl: "",
    terms: "",
    discountCode: "",
    maxRedemptions: "",
    isActive: true
  });

  // Fetch user's businesses for dropdown
  const { data: userBusinesses } = useQuery({
    queryKey: ['user-businesses', user?.id],
    queryFn: async () => {
      if (!user) return [];
      const { data, error } = await supabase
        .from('business_listings')
        .select('id, title')
        .eq('user_id', user.id);
      if (error) throw error;
      return data;
    },
    enabled: !!user,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      toast.error("Please sign in to submit an offer");
      return;
    }

    if (!formData.startDate || !formData.endDate) {
      toast.error("Please select start and end dates");
      return;
    }

    setLoading(true);
    
    try {
      const { error } = await supabase
        .from('special_offers')
        .insert({
          user_id: user.id,
          business_id: formData.businessId,
          title: formData.title,
          description: formData.description,
          discount: formData.discount,
          category: formData.category,
          location: formData.location,
          start_date: formData.startDate.toISOString(),
          end_date: formData.endDate.toISOString(),
          image_url: formData.imageUrl,
          terms: formData.terms,
          discount_code: formData.discountCode || null,
          max_redemptions: formData.maxRedemptions ? parseInt(formData.maxRedemptions) : null,
          is_active: formData.isActive,
          status: 'pending'
        });

      if (error) throw error;

      toast.success("Special offer submitted successfully! We'll review it and publish it soon.");
      setTimeout(() => navigate('/offers'), 2000);
    } catch (error) {
      console.error('Error submitting offer:', error);
      toast.error("Failed to submit offer. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="py-8 bg-slate-50">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-6 text-center">Submit Special Offer</h1>
          
          <form onSubmit={handleSubmit} className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Offer Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Select value={formData.businessId} onValueChange={(value) => setFormData({...formData, businessId: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Your Business" />
                  </SelectTrigger>
                  <SelectContent>
                    {userBusinesses?.map((business) => (
                      <SelectItem key={business.id} value={business.id}>
                        {business.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Input
                  placeholder="Offer Title (e.g., 50% Off Sushi Platters)"
                  value={formData.title}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  required
                />
                
                <Textarea
                  placeholder="Detailed description of the offer"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  rows={3}
                  required
                />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    placeholder="Discount (e.g., 50% OFF, R199)"
                    value={formData.discount}
                    onChange={(e) => setFormData({...formData, discount: e.target.value})}
                    required
                  />
                  
                  <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Restaurants">Restaurants</SelectItem>
                      <SelectItem value="Accommodation">Accommodation</SelectItem>
                      <SelectItem value="Beauty & Wellness">Beauty & Wellness</SelectItem>
                      <SelectItem value="Retail">Retail</SelectItem>
                      <SelectItem value="Education">Education</SelectItem>
                      <SelectItem value="Financial Services">Financial Services</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <Input
                  placeholder="Location (e.g., Cape Town, Western Cape)"
                  value={formData.location}
                  onChange={(e) => setFormData({...formData, location: e.target.value})}
                  required
                />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.startDate ? format(formData.startDate, "PPP") : "Start Date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.startDate}
                        onSelect={(date) => setFormData({...formData, startDate: date})}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.endDate ? format(formData.endDate, "PPP") : "End Date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.endDate}
                        onSelect={(date) => setFormData({...formData, endDate: date})}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                
                <Input
                  placeholder="Image URL (optional)"
                  value={formData.imageUrl}
                  onChange={(e) => setFormData({...formData, imageUrl: e.target.value})}
                />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    placeholder="Discount Code (optional)"
                    value={formData.discountCode}
                    onChange={(e) => setFormData({...formData, discountCode: e.target.value})}
                  />
                  
                  <Input
                    type="number"
                    placeholder="Max Redemptions (optional)"
                    value={formData.maxRedemptions}
                    onChange={(e) => setFormData({...formData, maxRedemptions: e.target.value})}
                  />
                </div>
                
                <Textarea
                  placeholder="Terms and conditions (optional)"
                  value={formData.terms}
                  onChange={(e) => setFormData({...formData, terms: e.target.value})}
                  rows={3}
                />
              </CardContent>
            </Card>

            <div className="text-center">
              <Button type="submit" disabled={loading} className="bg-[#007749] hover:bg-[#006739] px-8">
                {loading ? 'Submitting...' : 'Submit Offer'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default AddSpecialOffer;