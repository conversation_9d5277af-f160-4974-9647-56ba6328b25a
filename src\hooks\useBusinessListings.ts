
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export type BusinessListing = {
  id: string;
  title: string;
  description: string | null;
  category_id: string | null;
  full_address: string | null;
  city: string | null;
  phone: string | null;
  website: string | null;
  logo_url: string | null;
  images: string[] | null;
  subscription_tier: string | null;
  featured: boolean | null;
  verified: boolean | null;
  premium_placement_score?: number | null;
  price_range: string | null;
  status: string | null;
  created_at: string | null;
  categories?: {
    name: string;
    slug: string;
  };
};

export const useBusinessListings = (filters?: {
  category?: string;
  location?: string;
  searchTerm?: string;
  limit?: number;
}) => {
  return useQuery({
    queryKey: ['business-listings', filters],
    queryFn: async () => {
      console.log('Fetching business listings with filters:', filters);

      try {
        // Query with featured and created_at ordering (removed premium_placement_score)
        let query = supabase
          .from('business_listings')
          .select(`
            *,
            categories (
              name,
              slug
            )
          `)
          .eq('status', 'approved')
          .order('featured', { ascending: false })
          .order('created_at', { ascending: false });

        if (filters?.searchTerm) {
          query = query.or(`title.ilike.%${filters.searchTerm}%,description.ilike.%${filters.searchTerm}%`);
        }

        if (filters?.location) {
          query = query.or(`city.ilike.%${filters.location}%,full_address.ilike.%${filters.location}%`);
        }

        if (filters?.category) {
          query = query.eq('categories.slug', filters.category);
        }

        if (filters?.limit) {
          query = query.limit(filters.limit);
        }

        const { data, error } = await query;

        if (error) {
          console.error('Supabase error details:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code
          });
          throw error;
        }

        console.log('Fetched business listings:', data);
        return (data || []) as BusinessListing[];
      } catch (error) {
        console.error('Unexpected error in useBusinessListings:', error);
        throw error;
      }
    },
    retry: 1,
    retryDelay: 1000,
  });
};
