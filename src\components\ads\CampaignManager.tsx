
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { 
  Plus, 
  Play, 
  Pause, 
  Edit, 
  Trash2, 
  Target
} from 'lucide-react';
import { toast } from 'sonner';

interface Ad {
  id: string;
  title: string;
  description: string | null;
  ad_type: string;
  position: string;
  status: string;
  budget_total: number;
  budget_spent: number;
  start_date: string;
  end_date: string | null;
  clicks: number;
  impressions: number;
  created_at: string;
  business_id: string;
  target_url: string;
}

const CampaignManager = () => {
  const { user } = useAuth();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingAd, setEditingAd] = useState<Ad | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    ad_type: 'banner',
    position: 'top',
    budget_total: '',
    start_date: '',
    end_date: '',
    target_url: ''
  });

  // Get user's business listings
  const { data: userBusinesses } = useQuery({
    queryKey: ['user-businesses', user?.id],
    queryFn: async () => {
      if (!user) return [];
      const { data, error } = await supabase
        .from('business_listings')
        .select('id, title')
        .eq('user_id', user.id);
      if (error) throw error;
      return data;
    },
    enabled: !!user,
  });

  // Get ads (using existing ads table)
  const { data: ads, isLoading, refetch } = useQuery({
    queryKey: ['user-ads', user?.id],
    queryFn: async () => {
      if (!user || !userBusinesses || userBusinesses.length === 0) return [];

      const businessIds = userBusinesses.map(b => b.id);
      const { data, error } = await supabase
        .from('ads')
        .select('*')
        .in('business_id', businessIds)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Ad[];
    },
    enabled: !!user && !!userBusinesses && userBusinesses.length > 0,
  });

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      ad_type: 'banner',
      position: 'top',
      budget_total: '',
      start_date: '',
      end_date: '',
      target_url: ''
    });
    setEditingAd(null);
    setShowCreateForm(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !userBusinesses || userBusinesses.length === 0) return;

    setIsSubmitting(true);

    try {
      const adData = {
        business_id: userBusinesses[0].id,
        title: formData.title,
        description: formData.description || null,
        ad_type: formData.ad_type,
        position: formData.position,
        budget_total: parseInt(formData.budget_total) * 100,
        start_date: formData.start_date,
        end_date: formData.end_date || null,
        target_url: formData.target_url,
        status: 'pending'
      };

      if (editingAd) {
        const { error } = await supabase
          .from('ads')
          .update(adData)
          .eq('id', editingAd.id);

        if (error) throw error;
        toast.success('Ad updated successfully!');
      } else {
        const { error } = await supabase
          .from('ads')
          .insert(adData);

        if (error) throw error;
        toast.success('Ad created successfully!');
      }

      resetForm();
      refetch();
    } catch (error) {
      console.error('Error saving ad:', error);
      toast.error('Failed to save ad');
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateAdStatus = async (adId: string, status: string) => {
    try {
      const { error } = await supabase
        .from('ads')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', adId);

      if (error) throw error;

      toast.success(`Ad ${status === 'active' ? 'activated' : 'paused'} successfully!`);
      refetch();
    } catch (error) {
      console.error('Error updating ad status:', error);
      toast.error('Failed to update ad status');
    }
  };

  const deleteAd = async (adId: string) => {
    if (!confirm('Are you sure you want to delete this ad?')) return;

    try {
      const { error } = await supabase
        .from('ads')
        .delete()
        .eq('id', adId);

      if (error) throw error;

      toast.success('Ad deleted successfully!');
      refetch();
    } catch (error) {
      console.error('Error deleting ad:', error);
      toast.error('Failed to delete ad');
    }
  };

  const editAd = (ad: Ad) => {
    setFormData({
      title: ad.title,
      description: ad.description || '',
      ad_type: ad.ad_type,
      position: ad.position,
      budget_total: (ad.budget_total / 100).toString(),
      start_date: ad.start_date.split('T')[0],
      end_date: ad.end_date ? ad.end_date.split('T')[0] : '',
      target_url: ad.target_url || ''
    });
    setEditingAd(ad);
    setShowCreateForm(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Target className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600">Sign in to manage your ads</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-800">Ad Manager</h2>
          <p className="text-slate-600">Create and manage your advertising campaigns</p>
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="bg-[#007749] hover:bg-[#006739]"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Ad
        </Button>
      </div>

      {/* Create/Edit Form */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingAd ? 'Edit Ad' : 'Create New Ad'}
            </CardTitle>
            <CardDescription>
              Set up your advertisement with targeting and budget settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Ad Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    required
                    placeholder="Summer Sale Ad"
                  />
                </div>

                <div>
                  <Label htmlFor="budget_total">Budget (R) *</Label>
                  <Input
                    id="budget_total"
                    type="number"
                    value={formData.budget_total}
                    onChange={(e) => setFormData(prev => ({ ...prev, budget_total: e.target.value }))}
                    required
                    placeholder="1000"
                    min="1"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="target_url">Target URL *</Label>
                <Input
                  id="target_url"
                  type="url"
                  value={formData.target_url}
                  onChange={(e) => setFormData(prev => ({ ...prev, target_url: e.target.value }))}
                  required
                  placeholder="https://your-website.com"
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe your ad..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="ad_type">Ad Type</Label>
                  <Select 
                    value={formData.ad_type}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, ad_type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="banner">Banner</SelectItem>
                      <SelectItem value="sidebar">Sidebar</SelectItem>
                      <SelectItem value="sponsored_listing">Sponsored Listing</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="position">Position</Label>
                  <Select 
                    value={formData.position}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, position: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="top">Top</SelectItem>
                      <SelectItem value="middle">Middle</SelectItem>
                      <SelectItem value="bottom">Bottom</SelectItem>
                      <SelectItem value="sidebar">Sidebar</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="start_date">Start Date *</Label>
                  <Input
                    id="start_date"
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
                    required
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="end_date">End Date (Optional)</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
                  min={formData.start_date}
                />
              </div>

              <div className="flex gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={resetForm}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 bg-[#007749] hover:bg-[#006739]"
                >
                  {isSubmitting ? 'Saving...' : editingAd ? 'Update Ad' : 'Create Ad'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Ads List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Ads</CardTitle>
          <CardDescription>
            Manage your active and draft advertisements
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#007749] mx-auto mb-4"></div>
              <p className="text-slate-600">Loading ads...</p>
            </div>
          ) : !ads || ads.length === 0 ? (
            <div className="text-center py-8">
              <Target className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-600 mb-2">No ads yet</p>
              <p className="text-sm text-slate-500">Create your first ad to start advertising</p>
            </div>
          ) : (
            <div className="space-y-4">
              {ads.map((ad) => (
                <Card key={ad.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg">{ad.title}</h3>
                          <Badge className={getStatusColor(ad.status)}>
                            {ad.status}
                          </Badge>
                        </div>
                        
                        {ad.description && (
                          <p className="text-slate-600 mb-3">{ad.description}</p>
                        )}
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-slate-500">Budget</p>
                            <p className="font-semibold">R{(ad.budget_total / 100).toFixed(2)}</p>
                          </div>
                          <div>
                            <p className="text-slate-500">Spent</p>
                            <p className="font-semibold">R{(ad.budget_spent / 100).toFixed(2)}</p>
                          </div>
                          <div>
                            <p className="text-slate-500">Clicks</p>
                            <p className="font-semibold">{ad.clicks}</p>
                          </div>
                          <div>
                            <p className="text-slate-500">Impressions</p>
                            <p className="font-semibold">{ad.impressions}</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {ad.status === 'pending' || ad.status === 'paused' ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateAdStatus(ad.id, 'active')}
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateAdStatus(ad.id, 'paused')}
                          >
                            <Pause className="h-4 w-4" />
                          </Button>
                        )}
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => editAd(ad)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => deleteAd(ad.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CampaignManager;
