
import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Star, 
  Camera, 
  Upload,
  Award,
  Calendar
} from 'lucide-react';
import { toast } from 'sonner';

interface ReviewFormProps {
  businessId: string;
  onClose?: () => void;
}

const ReviewForm = ({ businessId, onClose }: ReviewFormProps) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [rating, setRating] = useState(0);
  const [reviewTitle, setReviewTitle] = useState('');
  const [reviewText, setReviewText] = useState('');
  const [recommended, setRecommended] = useState(true);
  const [verifiedPurchase, setVerifiedPurchase] = useState(false);
  const [visitDate, setVisitDate] = useState('');
  const [images, setImages] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const submitReviewMutation = useMutation({
    mutationFn: async (reviewData: {
      business_id: string;
      user_id: string;
      rating: number;
      review_title?: string;
      review_text?: string;
      recommended: boolean;
      verified_purchase: boolean;
      visit_date?: string;
      images?: string[];
    }) => {
      const { error } = await supabase
        .from('business_reviews')
        .insert(reviewData);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('Review submitted successfully!');
      // Reset form
      setRating(0);
      setReviewTitle('');
      setReviewText('');
      setRecommended(true);
      setVerifiedPurchase(false);
      setVisitDate('');
      setImages([]);
      // Refetch reviews
      queryClient.invalidateQueries({ queryKey: ['business-reviews', businessId] });
      if (onClose) onClose();
    },
    onError: (error) => {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review');
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      toast.error('Please sign in to submit a review');
      return;
    }

    if (rating === 0) {
      toast.error('Please select a rating');
      return;
    }

    setIsSubmitting(true);

    try {
      await submitReviewMutation.mutateAsync({
        business_id: businessId,
        user_id: user.id,
        rating,
        review_title: reviewTitle || null,
        review_text: reviewText || null,
        recommended,
        verified_purchase: verifiedPurchase,
        visit_date: visitDate || null,
        images: images.length > 0 ? images : null,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStars = () => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => setRating(star)}
            className="focus:outline-none"
          >
            <Star
              className={`h-8 w-8 cursor-pointer transition-colors ${
                star <= rating 
                  ? 'fill-yellow-400 text-yellow-400' 
                  : 'text-gray-300 hover:text-yellow-400'
              }`}
            />
          </button>
        ))}
        {rating > 0 && (
          <span className="ml-2 text-sm text-slate-600">
            {rating} of 5 stars
          </span>
        )}
      </div>
    );
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Star className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600 mb-4">Please sign in to leave a review</p>
          <p className="text-sm text-slate-500">
            Share your experience and earn reward points!
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Star className="h-5 w-5 text-[#007749]" />
          Write a Review
        </CardTitle>
        <CardDescription>
          Share your experience and help other customers make informed decisions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Rating */}
          <div>
            <Label className="text-base font-medium mb-3 block">
              Overall Rating *
            </Label>
            {renderStars()}
          </div>

          {/* Review Title */}
          <div>
            <Label htmlFor="reviewTitle" className="text-base font-medium">
              Review Title (Optional)
            </Label>
            <Input
              id="reviewTitle"
              value={reviewTitle}
              onChange={(e) => setReviewTitle(e.target.value)}
              placeholder="Sum up your experience in a few words..."
              className="mt-2"
            />
          </div>

          {/* Review Text */}
          <div>
            <Label htmlFor="reviewText" className="text-base font-medium">
              Your Review (Optional)
            </Label>
            <Textarea
              id="reviewText"
              value={reviewText}
              onChange={(e) => setReviewText(e.target.value)}
              placeholder="Tell others about your experience..."
              rows={4}
              className="mt-2"
            />
          </div>

          {/* Visit Date */}
          <div>
            <Label htmlFor="visitDate" className="text-base font-medium">
              Visit Date (Optional)
            </Label>
            <div className="mt-2">
              <Input
                id="visitDate"
                type="date"
                value={visitDate}
                onChange={(e) => setVisitDate(e.target.value)}
                max={new Date().toISOString().split('T')[0]}
              />
            </div>
          </div>

          {/* Switches */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Award className="h-4 w-4 text-[#007749]" />
                <Label htmlFor="recommended" className="text-base font-medium">
                  Would you recommend this business?
                </Label>
              </div>
              <Switch
                id="recommended"
                checked={recommended}
                onCheckedChange={setRecommended}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-green-600" />
                <Label htmlFor="verifiedPurchase" className="text-base font-medium">
                  This is a verified purchase/visit
                </Label>
              </div>
              <Switch
                id="verifiedPurchase"
                checked={verifiedPurchase}
                onCheckedChange={setVerifiedPurchase}
              />
            </div>
          </div>

          {/* Photo Upload Section */}
          <div>
            <Label className="text-base font-medium mb-3 block">
              Add Photos (Optional)
            </Label>
            <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
              <Camera className="h-8 w-8 text-slate-400 mx-auto mb-2" />
              <p className="text-sm text-slate-600 mb-2">
                Add photos to make your review more helpful
              </p>
              <Button type="button" variant="outline" size="sm" disabled>
                <Upload className="h-4 w-4 mr-1" />
                Upload Photos
              </Button>
              <p className="text-xs text-slate-500 mt-2">
                Photo upload coming soon!
              </p>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex items-center justify-end gap-3">
            {onClose && (
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting || rating === 0}
              className="bg-[#007749] hover:bg-[#006739]"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Review'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ReviewForm;
