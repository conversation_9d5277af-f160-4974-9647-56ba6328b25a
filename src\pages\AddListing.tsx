import React, { useState } from "react";
import Layout from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";
import { FileUpload } from "@/components/ui/file-upload";
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Link as LinkIcon,
  Upload,
  Plus,
  X,
  Clock
} from "lucide-react";

const priceRanges = [
  { id: "not-to-say", name: "Not to say" },
  { id: "inexpensive", name: "R - Inexpensive" },
  { id: "moderate", name: "RR - Moderate" },
  { id: "pricey", name: "RRR - Pricey" },
  { id: "ultra-high", name: "RRRR - Ultra High" },
];

const socialMediaPlatforms = [
  { id: "instagram", name: "Instagram" },
  { id: "youtube", name: "Youtube" },
  { id: "linkedin", name: "LinkedIn" },
  { id: "facebook", name: "Facebook" },
  { id: "twitter", name: "Twitter" },
];

const AddListing = () => {
  const navigate = useNavigate();
  
  // Fetch categories from database
  const { data: categories, isLoading: categoriesLoading } = useQuery({
    queryKey: ['categories-for-listing'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');
      
      if (error) throw error;
      return data;
    }
  });

  // State for form fields
  const [listingTitle, setListingTitle] = useState("");
  const [category, setCategory] = useState("");
  const [fullAddress, setFullAddress] = useState("");
  const [city, setCity] = useState("");
  const [phone, setPhone] = useState("");
  const [website, setWebsite] = useState("");
  const [description, setDescription] = useState("");
  const [priceRange, setPriceRange] = useState("");
  const [priceFrom, setPriceFrom] = useState("");
  const [priceTo, setPriceTo] = useState("");
  const [businessHours, setBusinessHours] = useState({
    monday: { open: "09:00 AM", close: "05:00 PM", isOpen: true },
    tuesday: { open: "09:00 AM", close: "05:00 PM", isOpen: true },
    wednesday: { open: "09:00 AM", close: "05:00 PM", isOpen: true },
    thursday: { open: "09:00 AM", close: "05:00 PM", isOpen: true },
    friday: { open: "09:00 AM", close: "05:00 PM", isOpen: true },
    saturday: { open: "09:00 AM", close: "05:00 PM", isOpen: false },
    sunday: { open: "09:00 AM", close: "05:00 PM", isOpen: false },
  });
  const [socialMedia, setSocialMedia] = useState({ platform: "facebook", url: "" });
  const [socialMediaLinks, setSocialMediaLinks] = useState<{platform: string, url: string}[]>([]);
  const [tags, setTags] = useState("");
  const [faqs, setFaqs] = useState<{question: string, answer: string}[]>([
    { question: "", answer: "" }
  ]);
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [images, setImages] = useState<string[]>([]);
  const [logoUrl, setLogoUrl] = useState<string>("");

  // Add social media link
  const addSocialMediaLink = () => {
    if (socialMedia.url) {
      setSocialMediaLinks([...socialMediaLinks, socialMedia]);
      setSocialMedia({ platform: "facebook", url: "" });
    }
  };

  // Remove social media link
  const removeSocialMediaLink = (index: number) => {
    const updatedLinks = [...socialMediaLinks];
    updatedLinks.splice(index, 1);
    setSocialMediaLinks(updatedLinks);
  };

  // Add FAQ
  const addFaq = () => {
    setFaqs([...faqs, { question: "", answer: "" }]);
  };

  // Update FAQ
  const updateFaq = (index: number, field: 'question' | 'answer', value: string) => {
    const updatedFaqs = [...faqs];
    updatedFaqs[index][field] = value;
    setFaqs(updatedFaqs);
  };

  // Remove FAQ
  const removeFaq = (index: number) => {
    if (faqs.length > 1) {
      const updatedFaqs = [...faqs];
      updatedFaqs.splice(index, 1);
      setFaqs(updatedFaqs);
    }
  };

  // Handle image uploads
  const handleImagesUpload = (urls: string[]) => {
    setImages(prev => [...prev, ...urls]);
  };

  // Handle logo upload
  const handleLogoUpload = (urls: string[]) => {
    if (urls.length > 0) {
      setLogoUrl(urls[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!agreeToTerms) {
      toast.error("You must agree to the terms and conditions");
      return;
    }

    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      toast.error("You must be logged in to submit a listing");
      return;
    }

    try {
      // Find the category ID from the selected category slug
      const selectedCategory = categories?.find(cat => cat.slug === category);
      
      // Prepare the data for insertion
      const listingData = {
        user_id: user.id,
        title: listingTitle,
        category_id: selectedCategory?.id || null,
        full_address: fullAddress,
        city: city,
        phone: phone,
        website: website,
        description: description,
        price_range: priceRange,
        price_from: priceFrom ? parseFloat(priceFrom) : null,
        price_to: priceTo ? parseFloat(priceTo) : null,
        business_hours: businessHours,
        social_media_links: socialMediaLinks,
        tags: tags ? tags.split(',').map(tag => tag.trim()) : null,
        images: images.length > 0 ? images : null,
        logo_url: logoUrl || null,
        status: 'pending'
      };

      console.log('Submitting listing data:', listingData);

      // Insert the business listing
      const { data: listing, error: listingError } = await supabase
        .from('business_listings')
        .insert([listingData])
        .select()
        .single();

      if (listingError) {
        console.error('Error inserting listing:', listingError);
        toast.error("Failed to submit listing. Please try again.");
        return;
      }

      console.log('Listing created successfully:', listing);

      // Insert FAQs if they exist
      if (faqs.some(faq => faq.question.trim() && faq.answer.trim())) {
        const faqData = faqs
          .filter(faq => faq.question.trim() && faq.answer.trim())
          .map(faq => ({
            business_id: listing.id,
            question: faq.question,
            answer: faq.answer
          }));

        const { error: faqError } = await supabase
          .from('business_faqs')
          .insert(faqData);

        if (faqError) {
          console.error('Error inserting FAQs:', faqError);
          // Don't fail the whole submission for FAQ errors
          toast.error("Listing submitted but FAQs could not be saved");
        }
      }

      toast.success("Listing submitted successfully! We'll review it and get back to you soon.");
      
      // Reset form or navigate to a success page
      setTimeout(() => {
        navigate('/');
      }, 2000);

    } catch (error) {
      console.error('Unexpected error:', error);
      toast.error("An unexpected error occurred. Please try again.");
    }
  };

  return (
    <Layout>
      <div className="py-8 bg-slate-50">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-6 text-center">Submit Your Listing</h1>

          <div className="max-w-4xl mx-auto">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Primary Listing Details */}
              <Card>
                <CardHeader className="bg-slate-100 border-b">
                  <CardTitle className="text-lg">PRIMARY LISTING DETAILS</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center mb-1">
                        <label htmlFor="listingTitle" className="block text-sm font-medium">Listing Title</label>
                        <span className="text-red-500 ml-1">*</span>
                      </div>
                      <Input
                        id="listingTitle"
                        value={listingTitle}
                        onChange={(e) => setListingTitle(e.target.value)}
                        placeholder="Enter your listing title"
                        required
                      />
                    </div>

                    <div>
                      <div className="flex items-center mb-1">
                        <label htmlFor="fullAddress" className="block text-sm font-medium">Full Address</label>
                        <span className="text-red-500 ml-1">*</span>
                      </div>
                      <div className="flex gap-2 mb-2">
                        <Button type="button" size="sm" variant="outline" className="text-xs">
                          Search by Google
                        </Button>
                        <Button type="button" size="sm" variant="outline" className="text-xs">
                          Manual Coordinates
                        </Button>
                        <Button type="button" size="sm" variant="outline" className="text-xs">
                          Drop Pin
                        </Button>
                      </div>
                      <Textarea
                        id="fullAddress"
                        value={fullAddress}
                        onChange={(e) => setFullAddress(e.target.value)}
                        placeholder="Full address of your listing"
                        rows={3}
                        required
                      />
                    </div>

                    <div>
                      <div className="flex items-center mb-1">
                        <label htmlFor="city" className="block text-sm font-medium">City</label>
                      </div>
                      <Input
                        id="city"
                        value={city}
                        onChange={(e) => setCity(e.target.value)}
                        placeholder="City"
                      />
                      <p className="text-xs text-slate-500 mt-1">The city name will help users find you in search filters.</p>
                    </div>

                    <div>
                      <div className="flex items-center mb-1">
                        <label htmlFor="phone" className="block text-sm font-medium">Phone</label>
                      </div>
                      <Input
                        id="phone"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        placeholder="Phone number"
                        type="tel"
                      />
                    </div>

                    <div>
                      <div className="flex items-center mb-1">
                        <label htmlFor="website" className="block text-sm font-medium">Website</label>
                      </div>
                      <Input
                        id="website"
                        value={website}
                        onChange={(e) => setWebsite(e.target.value)}
                        placeholder="https://your-website.co.za"
                        type="url"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Category & Services */}
              <Card>
                <CardHeader className="bg-slate-100 border-b">
                  <CardTitle className="text-lg">CATEGORY & SERVICES</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div>
                    <div className="flex items-center mb-1">
                      <label htmlFor="category" className="block text-sm font-medium">Category</label>
                      <span className="text-red-500 ml-1">*</span>
                    </div>
                    <Select value={category} onValueChange={setCategory} disabled={categoriesLoading}>
                      <SelectTrigger>
                        <SelectValue placeholder={categoriesLoading ? "Loading categories..." : "Select Category"} />
                      </SelectTrigger>
                      <SelectContent>
                        {categories?.map((cat) => (
                          <SelectItem key={cat.id} value={cat.slug}>
                            {cat.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* Price Details */}
              <Card>
                <CardHeader className="bg-slate-100 border-b">
                  <CardTitle className="text-lg">PRICE DETAILS</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center mb-1">
                        <label className="block text-sm font-medium">Price Range</label>
                      </div>
                      <RadioGroup
                        value={priceRange}
                        onValueChange={setPriceRange}
                        className="flex flex-wrap gap-4"
                      >
                        {priceRanges.map((range) => (
                          <div key={range.id} className="flex items-center space-x-2">
                            <RadioGroupItem value={range.id} id={range.id} />
                            <Label htmlFor={range.id}>{range.name}</Label>
                          </div>
                        ))}
                      </RadioGroup>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <div className="flex items-center mb-1">
                          <label htmlFor="priceFrom" className="block text-sm font-medium">Price From</label>
                        </div>
                        <Input
                          id="priceFrom"
                          value={priceFrom}
                          onChange={(e) => setPriceFrom(e.target.value)}
                          placeholder="Price from"
                        />
                      </div>
                      <div>
                        <div className="flex items-center mb-1">
                          <label htmlFor="priceTo" className="block text-sm font-medium">Price To</label>
                        </div>
                        <Input
                          id="priceTo"
                          value={priceTo}
                          onChange={(e) => setPriceTo(e.target.value)}
                          placeholder="Price to"
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Business Hours */}
              <Card>
                <CardHeader className="bg-slate-100 border-b">
                  <CardTitle className="text-lg">BUSINESS HOURS</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    {Object.entries(businessHours).map(([day, hours]) => (
                      <div key={day} className="flex items-center">
                        <div className="w-24 font-medium capitalize">{day}</div>
                        <div className="flex-1 flex items-center gap-2">
                          <Select
                            value={hours.open}
                            onValueChange={(value) => setBusinessHours({
                              ...businessHours,
                              [day]: { ...hours, open: value }
                            })}
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue placeholder="Open" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="12:00 AM">12:00 AM</SelectItem>
                              <SelectItem value="01:00 AM">01:00 AM</SelectItem>
                              <SelectItem value="02:00 AM">02:00 AM</SelectItem>
                              <SelectItem value="03:00 AM">03:00 AM</SelectItem>
                              <SelectItem value="04:00 AM">04:00 AM</SelectItem>
                              <SelectItem value="05:00 AM">05:00 AM</SelectItem>
                              <SelectItem value="06:00 AM">06:00 AM</SelectItem>
                              <SelectItem value="07:00 AM">07:00 AM</SelectItem>
                              <SelectItem value="08:00 AM">08:00 AM</SelectItem>
                              <SelectItem value="09:00 AM">09:00 AM</SelectItem>
                              <SelectItem value="10:00 AM">10:00 AM</SelectItem>
                              <SelectItem value="11:00 AM">11:00 AM</SelectItem>
                              <SelectItem value="12:00 PM">12:00 PM</SelectItem>
                              <SelectItem value="01:00 PM">01:00 PM</SelectItem>
                              <SelectItem value="02:00 PM">02:00 PM</SelectItem>
                              <SelectItem value="03:00 PM">03:00 PM</SelectItem>
                              <SelectItem value="04:00 PM">04:00 PM</SelectItem>
                              <SelectItem value="05:00 PM">05:00 PM</SelectItem>
                              <SelectItem value="06:00 PM">06:00 PM</SelectItem>
                              <SelectItem value="07:00 PM">07:00 PM</SelectItem>
                              <SelectItem value="08:00 PM">08:00 PM</SelectItem>
                              <SelectItem value="09:00 PM">09:00 PM</SelectItem>
                              <SelectItem value="10:00 PM">10:00 PM</SelectItem>
                              <SelectItem value="11:00 PM">11:00 PM</SelectItem>
                            </SelectContent>
                          </Select>
                          <span>-</span>
                          <Select
                            value={hours.close}
                            onValueChange={(value) => setBusinessHours({
                              ...businessHours,
                              [day]: { ...hours, close: value }
                            })}
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue placeholder="Close" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="12:00 AM">12:00 AM</SelectItem>
                              <SelectItem value="01:00 AM">01:00 AM</SelectItem>
                              <SelectItem value="02:00 AM">02:00 AM</SelectItem>
                              <SelectItem value="03:00 AM">03:00 AM</SelectItem>
                              <SelectItem value="04:00 AM">04:00 AM</SelectItem>
                              <SelectItem value="05:00 AM">05:00 AM</SelectItem>
                              <SelectItem value="06:00 AM">06:00 AM</SelectItem>
                              <SelectItem value="07:00 AM">07:00 AM</SelectItem>
                              <SelectItem value="08:00 AM">08:00 AM</SelectItem>
                              <SelectItem value="09:00 AM">09:00 AM</SelectItem>
                              <SelectItem value="10:00 AM">10:00 AM</SelectItem>
                              <SelectItem value="11:00 AM">11:00 AM</SelectItem>
                              <SelectItem value="12:00 PM">12:00 PM</SelectItem>
                              <SelectItem value="01:00 PM">01:00 PM</SelectItem>
                              <SelectItem value="02:00 PM">02:00 PM</SelectItem>
                              <SelectItem value="03:00 PM">03:00 PM</SelectItem>
                              <SelectItem value="04:00 PM">04:00 PM</SelectItem>
                              <SelectItem value="05:00 PM">05:00 PM</SelectItem>
                              <SelectItem value="06:00 PM">06:00 PM</SelectItem>
                              <SelectItem value="07:00 PM">07:00 PM</SelectItem>
                              <SelectItem value="08:00 PM">08:00 PM</SelectItem>
                              <SelectItem value="09:00 PM">09:00 PM</SelectItem>
                              <SelectItem value="10:00 PM">10:00 PM</SelectItem>
                              <SelectItem value="11:00 PM">11:00 PM</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="ml-4">
                          <Button
                            type="button"
                            size="sm"
                            variant="ghost"
                            className={`rounded-full p-1 ${hours.isOpen ? 'text-green-500' : 'text-red-500'}`}
                            onClick={() => setBusinessHours({
                              ...businessHours,
                              [day]: { ...hours, isOpen: !hours.isOpen }
                            })}
                          >
                            {hours.isOpen ? (
                              <div className="h-5 w-5 rounded-full bg-green-100 flex items-center justify-center">
                                <span className="text-xs">✓</span>
                              </div>
                            ) : (
                              <div className="h-5 w-5 rounded-full bg-red-100 flex items-center justify-center">
                                <span className="text-xs">✕</span>
                              </div>
                            )}
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Social Media */}
              <Card>
                <CardHeader className="bg-slate-100 border-b">
                  <CardTitle className="text-lg">SOCIAL MEDIA</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="flex gap-2">
                      <Select
                        value={socialMedia.platform}
                        onValueChange={(value) => setSocialMedia({ ...socialMedia, platform: value })}
                      >
                        <SelectTrigger className="w-40">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent>
                          {socialMediaPlatforms.map((platform) => (
                            <SelectItem key={platform.id} value={platform.id}>
                              {platform.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Input
                        value={socialMedia.url}
                        onChange={(e) => setSocialMedia({ ...socialMedia, url: e.target.value })}
                        placeholder="Enter social media URL"
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        size="icon"
                        onClick={addSocialMediaLink}
                        className="bg-blue-500 hover:bg-blue-600"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>

                    {socialMediaLinks.length > 0 && (
                      <div className="space-y-2">
                        {socialMediaLinks.map((link, index) => (
                          <div key={index} className="flex items-center gap-2 bg-slate-50 p-2 rounded">
                            <div className="font-medium capitalize">{link.platform}:</div>
                            <div className="flex-1 text-sm truncate">{link.url}</div>
                            <Button
                              type="button"
                              size="icon"
                              variant="ghost"
                              onClick={() => removeSocialMediaLink(index)}
                              className="h-6 w-6 text-red-500 hover:text-red-700"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Frequently Asked Questions */}
              <Card>
                <CardHeader className="bg-slate-100 border-b">
                  <CardTitle className="text-lg">FREQUENTLY ASKED QUESTIONS</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    {faqs.map((faq, index) => (
                      <div key={index} className="space-y-2 p-4 border rounded-md">
                        <div className="flex justify-between items-center">
                          <h4 className="font-medium">FAQ</h4>
                          {faqs.length > 1 && (
                            <Button
                              type="button"
                              size="sm"
                              variant="ghost"
                              onClick={() => removeFaq(index)}
                              className="text-red-500 hover:text-red-700 h-6 px-2"
                            >
                              Remove
                            </Button>
                          )}
                        </div>
                        <Input
                          value={faq.question}
                          onChange={(e) => updateFaq(index, 'question', e.target.value)}
                          placeholder="Question"
                          className="mb-2"
                        />
                        <Textarea
                          value={faq.answer}
                          onChange={(e) => updateFaq(index, 'answer', e.target.value)}
                          placeholder="Answer"
                          rows={3}
                        />
                      </div>
                    ))}

                    <Button
                      type="button"
                      variant="outline"
                      onClick={addFaq}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add FAQ
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* More Info */}
              <Card>
                <CardHeader className="bg-slate-100 border-b">
                  <CardTitle className="text-lg">MORE INFO</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    <div>
                      <div className="flex items-center mb-1">
                        <label htmlFor="description" className="block text-sm font-medium">Description</label>
                        <span className="text-red-500 ml-1">*</span>
                      </div>
                      <div className="border rounded-md">
                        <div className="flex items-center gap-1 p-2 border-b">
                          <Button type="button" size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <Bold className="h-4 w-4" />
                          </Button>
                          <Button type="button" size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <Italic className="h-4 w-4" />
                          </Button>
                          <Button type="button" size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <Underline className="h-4 w-4" />
                          </Button>
                          <div className="h-6 border-r mx-1"></div>
                          <Button type="button" size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <List className="h-4 w-4" />
                          </Button>
                          <Button type="button" size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <ListOrdered className="h-4 w-4" />
                          </Button>
                          <div className="h-6 border-r mx-1"></div>
                          <Button type="button" size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <AlignLeft className="h-4 w-4" />
                          </Button>
                          <Button type="button" size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <AlignCenter className="h-4 w-4" />
                          </Button>
                          <Button type="button" size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <AlignRight className="h-4 w-4" />
                          </Button>
                          <div className="h-6 border-r mx-1"></div>
                          <Button type="button" size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <LinkIcon className="h-4 w-4" />
                          </Button>
                        </div>
                        <Textarea
                          id="description"
                          value={description}
                          onChange={(e) => setDescription(e.target.value)}
                          placeholder="Detailed description about your listing"
                          rows={6}
                          className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center mb-1">
                        <label htmlFor="tags" className="block text-sm font-medium">Tags or keywords (Comma separated)</label>
                      </div>
                      <Input
                        id="tags"
                        value={tags}
                        onChange={(e) => setTags(e.target.value)}
                        placeholder="Enter tags separated by commas"
                      />
                      <p className="text-xs text-slate-500 mt-1">
                        These keywords or tags will help your listing to find in search. Add a comma separated list of keywords related to your business.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Media */}
              <Card>
                <CardHeader className="bg-slate-100 border-b">
                  <CardTitle className="text-lg">MEDIA</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    <div>
                      <div className="flex items-center mb-1">
                        <label className="block text-sm font-medium">Images</label>
                      </div>
                      <div className="border-2 border-dashed rounded-md p-8 text-center">
                        <Upload className="h-10 w-10 mx-auto text-slate-400 mb-2" />
                        <p className="text-sm text-slate-600 mb-2">Drop files here or click to upload</p>
                        <FileUpload
                          onFileUpload={handleImagesUpload}
                          multiple={true}
                          maxFiles={10}
                          buttonText="Browse Files"
                          folder="business-images"
                        />
                      </div>
                      {images.length > 0 && (
                        <div className="mt-2 text-sm text-gray-600">
                          {images.length} image(s) uploaded
                        </div>
                      )}
                    </div>

                    <div>
                      <div className="flex items-center mb-1">
                        <label className="block text-sm font-medium">Upload Business Logo</label>
                      </div>
                      <div className="border-2 border-dashed rounded-md p-6 text-center">
                        <p className="text-sm text-slate-600 mb-2">Drop files here or click to upload</p>
                        <FileUpload
                          onFileUpload={handleLogoUpload}
                          multiple={false}
                          maxFiles={1}
                          buttonText="Browse Files"
                          folder="business-logos"
                        />
                      </div>
                      {logoUrl && (
                        <div className="mt-2 text-sm text-gray-600">
                          Logo uploaded successfully
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Terms and Conditions */}
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="terms"
                  checked={agreeToTerms}
                  onCheckedChange={(checked) => setAgreeToTerms(checked as boolean)}
                />
                <div className="grid gap-1.5 leading-none">
                  <label
                    htmlFor="terms"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    I Agree
                  </label>
                  <p className="text-sm text-slate-500">
                    You agree you accept our Terms & Conditions for posting this ad.
                  </p>
                </div>
              </div>

              {/* Submit Button */}
              <div className="bg-slate-100 p-4 rounded-md flex justify-center">
                <Button type="submit" className="bg-[#007749] hover:bg-[#006739] px-8">
                  Submit Listing
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AddListing;
