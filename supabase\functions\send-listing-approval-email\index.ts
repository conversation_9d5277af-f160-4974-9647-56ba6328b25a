
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[LISTING-APPROVAL-EMAIL] ${step}${detailsStr}`);
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    logStep("Function started");

    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    const { listing_id, title, description, city, user_id, created_at } = await req.json();
    logStep("Request data", { listing_id, title, city });

    if (!listing_id) {
      throw new Error("Listing ID is required");
    }

    // Fetch additional listing details and user information
    const { data: listing, error: listingError } = await supabaseClient
      .from('business_listings')
      .select(`
        *,
        categories:category_id (name, slug),
        profiles:user_id (email, full_name)
      `)
      .eq('id', listing_id)
      .single();

    if (listingError || !listing) {
      logStep("Error fetching listing", listingError);
      throw new Error("Failed to fetch listing details");
    }

    logStep("Listing fetched", { title: listing.title, status: listing.status });

    // Only send email for pending listings
    if (listing.status !== 'pending') {
      logStep("Listing not pending, skipping email", { status: listing.status });
      return new Response(JSON.stringify({ message: "Email not sent - listing not pending" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      });
    }

    // Generate approval and rejection URLs
    const approvalUrl = `${Deno.env.get("SUPABASE_URL")}/functions/v1/approve-listing?listing_id=${listing_id}&action=approve`;
    const rejectionUrl = `${Deno.env.get("SUPABASE_URL")}/functions/v1/approve-listing?listing_id=${listing_id}&action=reject`;
    const dashboardUrl = `${req.headers.get("origin") || "https://sa360.co.za"}/admin/listings`;

    // Prepare email content
    const categoryName = listing.categories?.name || 'Uncategorized';
    const userEmail = listing.profiles?.email || 'Unknown';
    const userName = listing.profiles?.full_name || 'Unknown User';

    const emailHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>New Listing Approval Required</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .listing-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
            .button { display: inline-block; background: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
            .button.secondary { background: #dc2626; }
            .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>New Listing Approval Required</h1>
            </div>
            
            <div class="content">
              <p>A new business listing has been submitted and requires approval:</p>
              
              <div class="listing-details">
                <h3>${listing.title}</h3>
                <p><strong>Category:</strong> ${categoryName}</p>
                <p><strong>Location:</strong> ${listing.city}${listing.full_address ? `, ${listing.full_address}` : ''}</p>
                <p><strong>Phone:</strong> ${listing.phone || 'Not provided'}</p>
                <p><strong>Website:</strong> ${listing.website || 'Not provided'}</p>
                <p><strong>Submitted by:</strong> ${userName} (${userEmail})</p>
                <p><strong>Submitted on:</strong> ${new Date(listing.created_at).toLocaleString()}</p>
                
                ${listing.description ? `
                  <p><strong>Description:</strong></p>
                  <p>${listing.description}</p>
                ` : ''}
              </div>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${approvalUrl}" class="button">✅ Approve Listing</a>
                <a href="${rejectionUrl}" class="button secondary">❌ Reject Listing</a>
              </div>
              
              <p>You can also manage all listings in the <a href="${dashboardUrl}">admin dashboard</a> or directly in the Supabase database by updating the 'status' field in the 'business_listings' table.</p>
              
              <p><small>This email was automatically generated when a new listing was submitted to SA Business Beacon. To approve or reject this listing manually, go to your Supabase dashboard → Database → business_listings table and update the status field for listing ID: ${listing_id}</small></p>
            </div>
            
            <div class="footer">
              <p>SA Business Beacon - Business Directory</p>
              <p>This is an automated notification email.</p>
              <p>Listing ID: ${listing_id} | Status: ${listing.status}</p>
            </div>
          </div>
        </body>
      </html>
    `;

    // Send email using Supabase's built-in email functionality
    try {
      // Use Supabase's built-in email service through the admin auth API
      const { data: emailResult, error: emailError } = await supabaseClient.auth.admin.inviteUserByEmail(
        '<EMAIL>',
        {
          data: {
            custom_email_subject: `New Listing Approval Required: ${listing.title}`,
            custom_email_html: emailHtml,
            is_notification_email: true,
            listing_id: listing_id,
            listing_title: listing.title,
            admin_notification: true
          },
          redirectTo: dashboardUrl
        }
      );

      if (emailError) {
        logStep("Supabase email error", emailError);
        
        // Fallback: Create an email notification record for manual processing
        const { error: notificationError } = await supabaseClient
          .from('email_notifications')
          .insert({
            type: 'listing_approval',
            recipient_email: '<EMAIL>',
            subject: `New Listing Approval Required: ${listing.title}`,
            data: {
              listing_id,
              title: listing.title,
              category: categoryName,
              city: listing.city,
              user_name: userName,
              user_email: userEmail,
              approval_url: approvalUrl,
              rejection_url: rejectionUrl,
              dashboard_url: dashboardUrl,
              html_content: emailHtml
            },
            status: 'pending'
          });

        if (notificationError) {
          logStep("Failed to create notification record", notificationError);
        } else {
          logStep("Created email notification record for manual processing");
        }

        return new Response(JSON.stringify({ 
          success: true, 
          message: "Email notification queued for processing",
          fallback_used: true,
          listing_id: listing_id,
          manual_action_required: `Please check Supabase dashboard → business_listings table → listing ID: ${listing_id} to approve/reject manually`
        }), {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 200,
        });
      }

      logStep("Email sent successfully via Supabase", { emailId: emailResult?.user?.id });

      return new Response(JSON.stringify({ 
        success: true, 
        message: "Approval notification sent successfully via Supabase",
        listing_id: listing_id,
        manual_option: `Alternative: Go to Supabase dashboard → business_listings table → listing ID: ${listing_id} to approve/reject manually`
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      });

    } catch (supabaseEmailError) {
      logStep("Supabase email service error", supabaseEmailError);
      
      // Create notification record as fallback
      const { error: notificationError } = await supabaseClient
        .from('email_notifications')
        .insert({
          type: 'listing_approval',
          recipient_email: '<EMAIL>',
          subject: `New Listing Approval Required: ${listing.title}`,
          data: {
            listing_id,
            title: listing.title,
            category: categoryName,
            city: listing.city,
            user_name: userName,
            user_email: userEmail,
            approval_url: approvalUrl,
            rejection_url: rejectionUrl,
            dashboard_url: dashboardUrl,
            html_content: emailHtml
          },
          status: 'pending'
        });

      return new Response(JSON.stringify({ 
        success: true, 
        message: "Email notification queued. Manual approval available in Supabase dashboard.",
        listing_id: listing_id,
        manual_action: `Go to Supabase dashboard → business_listings table → listing ID: ${listing_id} to approve/reject manually`,
        fallback_notification_created: !notificationError
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      });
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logStep("ERROR", { message: errorMessage });
    
    return new Response(JSON.stringify({ 
      error: errorMessage,
      manual_action: "You can manually approve listings by going to Supabase dashboard → business_listings table and updating the status field"
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
