
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Eye, MousePointer, DollarSign } from 'lucide-react';

interface Ad {
  id: string;
  title: string;
  description: string | null;
  ad_type: string;
  position: string;
  status: string;
  clicks: number;
  impressions: number;
  budget_total: number;
  budget_spent: number;
  created_at: string;
}

export const SimpleCampaignManager: React.FC = () => {
  const { user } = useAuth();

  const { data: ads, isLoading, refetch } = useQuery({
    queryKey: ['user-ads', user?.id],
    queryFn: async () => {
      if (!user) return [];

      // Get user's businesses first
      const { data: businesses } = await supabase
        .from('business_listings')
        .select('id')
        .eq('user_id', user.id);

      if (!businesses || businesses.length === 0) return [];

      const businessIds = businesses.map(b => b.id);

      const { data, error } = await supabase
        .from('ads')
        .select('*')
        .in('business_id', businessIds)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Ad[];
    },
    enabled: !!user,
  });

  const handleStatusChange = async (adId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('ads')
        .update({ status: newStatus })
        .eq('id', adId);

      if (error) throw error;
      
      refetch();
    } catch (error) {
      console.error('Error updating ad status:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-slate-600">Please sign in to manage your campaigns</p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#007749] mx-auto mb-4"></div>
          <p className="text-slate-600">Loading your campaigns...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Campaign Manager</h2>
          <p className="text-slate-600">Manage your advertising campaigns</p>
        </div>
        <Button onClick={() => refetch()}>
          Refresh
        </Button>
      </div>

      {!ads || ads.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-slate-600">No campaigns found</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {ads.map((ad) => (
            <Card key={ad.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{ad.title}</CardTitle>
                  <Badge className={getStatusColor(ad.status)}>
                    {ad.status}
                  </Badge>
                </div>
                <CardDescription>{ad.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-slate-600">Impressions</p>
                      <p className="font-semibold">{ad.impressions.toLocaleString()}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <MousePointer className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-slate-600">Clicks</p>
                      <p className="font-semibold">{ad.clicks.toLocaleString()}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-orange-600" />
                    <div>
                      <p className="text-sm text-slate-600">Budget</p>
                      <p className="font-semibold">R{(ad.budget_total / 100).toFixed(2)}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-red-600" />
                    <div>
                      <p className="text-sm text-slate-600">Spent</p>
                      <p className="font-semibold">R{(ad.budget_spent / 100).toFixed(2)}</p>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  {ad.status === 'active' && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleStatusChange(ad.id, 'paused')}
                    >
                      Pause
                    </Button>
                  )}
                  {ad.status === 'paused' && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleStatusChange(ad.id, 'active')}
                    >
                      Resume
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
