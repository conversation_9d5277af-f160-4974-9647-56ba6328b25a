
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Mail, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export const EmailNotificationManager: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [processedCount, setProcessedCount] = useState(0);

  const handleProcessEmails = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('process-email-notifications');
      
      if (error) {
        console.error('Error processing emails:', error);
        toast.error('Failed to process emails');
        return;
      }

      setProcessedCount(data?.processed || 0);
      toast.success(`Processed ${data?.processed || 0} emails`);
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to process emails');
    } finally {
      setLoading(false);
    }
  };

  const handleTestEmail = async () => {
    setLoading(true);
    try {
      // Get a sample business listing to test with
      const { data: listings } = await supabase
        .from('business_listings')
        .select('*')
        .eq('status', 'pending')
        .limit(1);

      if (listings && listings.length > 0) {
        const { data, error } = await supabase.functions.invoke('send-listing-approval-email', {
          body: { listingId: listings[0].id }
        });

        if (error) {
          console.error('Error sending test email:', error);
          toast.error('Failed to send test email');
          return;
        }

        toast.success('Test email sent successfully');
      } else {
        toast.info('No pending listings to test with');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to send test email');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Email Notifications</h2>
          <p className="text-muted-foreground">
            Manage automated email notifications for listing approvals
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={handleProcessEmails} 
            disabled={loading}
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Process Emails
          </Button>
          <Button 
            onClick={handleTestEmail} 
            disabled={loading}
            variant="outline"
          >
            <Mail className="h-4 w-4 mr-2" />
            Test Email
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Email System</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Active</div>
            <p className="text-xs text-muted-foreground">
              Automated notifications enabled
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Processed</CardTitle>
            <Clock className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{processedCount}</div>
            <p className="text-xs text-muted-foreground">
              Emails in last batch
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Ready</div>
            <p className="text-xs text-muted-foreground">
              System operational
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Email Configuration</CardTitle>
          <CardDescription>
            Email notifications are sent automatically when new business listings are submitted
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <div className="font-medium">Listing Approval Emails</div>
                <div className="text-sm text-muted-foreground">
                  Sent to: <EMAIL>
                </div>
              </div>
              <Badge variant="default">Enabled</Badge>
            </div>
            
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <div className="font-medium">Email Provider</div>
                <div className="text-sm text-muted-foreground">
                  Resend API configured
                </div>
              </div>
              <Badge variant="default">Connected</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
