
import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar
} from 'recharts';
import { 
  Activity, 
  Eye, 
  MousePointer, 
  TrendingUp, 
  Clock,
  RefreshCw,
  Zap
} from 'lucide-react';

interface RealTimeData {
  timestamp: string;
  impressions: number;
  clicks: number;
  ctr: number;
}

interface LiveMetrics {
  totalImpressions: number;
  totalClicks: number;
  avgCtr: number;
  activeAds: number;
  topPerformingAd: string;
}

const RealTimeAnalytics = () => {
  const { user } = useAuth();
  const [isLive, setIsLive] = useState(true);
  const [liveData, setLiveData] = useState<RealTimeData[]>([]);
  const [liveMetrics, setLiveMetrics] = useState<LiveMetrics>({
    totalImpressions: 0,
    totalClicks: 0,
    avgCtr: 0,
    activeAds: 0,
    topPerformingAd: ''
  });

  // Get user's business listings
  const { data: userBusinesses } = useQuery({
    queryKey: ['user-businesses', user?.id],
    queryFn: async () => {
      if (!user) return [];
      const { data, error } = await supabase
        .from('business_listings')
        .select('id, title')
        .eq('user_id', user.id);
      if (error) throw error;
      return data;
    },
    enabled: !!user,
  });

  // Get real-time ad performance using existing ads table
  const { data: realtimeData, refetch } = useQuery({
    queryKey: ['realtime-ad-data', user?.id],
    queryFn: async () => {
      if (!user || !userBusinesses || userBusinesses.length === 0) return null;

      const businessIds = userBusinesses.map(b => b.id);

      // Get active ads with their performance data
      const { data: ads, error } = await supabase
        .from('ads')
        .select('id, title, impressions, clicks, business_id')
        .in('business_id', businessIds)
        .eq('status', 'active');

      if (error) throw error;

      const adsData = ads || [];

      // Calculate metrics
      const totalImpressions = adsData.reduce((sum, ad) => sum + ad.impressions, 0);
      const totalClicks = adsData.reduce((sum, ad) => sum + ad.clicks, 0);
      const avgCtr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;
      const topPerformingAd = adsData.sort((a, b) => {
        const aCtr = a.impressions > 0 ? (a.clicks / a.impressions) * 100 : 0;
        const bCtr = b.impressions > 0 ? (b.clicks / b.impressions) * 100 : 0;
        return bCtr - aCtr;
      })[0]?.title || 'N/A';

      // Generate sample hourly data for the last 12 hours
      const now = new Date();
      const hourlyData = Array.from({ length: 12 }, (_, i) => {
        const hour = new Date(now.getTime() - (11 - i) * 60 * 60 * 1000);
        
        // Simulate some data based on existing metrics
        const baseImpressions = Math.floor(totalImpressions / 24);
        const baseClicks = Math.floor(totalClicks / 24);
        const hourImpressions = Math.floor(baseImpressions * (0.5 + Math.random()));
        const hourClicks = Math.floor(baseClicks * (0.5 + Math.random()));
        const hourCtr = hourImpressions > 0 ? (hourClicks / hourImpressions) * 100 : 0;

        return {
          timestamp: hour.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
          impressions: hourImpressions,
          clicks: hourClicks,
          ctr: parseFloat(hourCtr.toFixed(2))
        };
      });

      return {
        metrics: {
          totalImpressions,
          totalClicks,
          avgCtr: parseFloat(avgCtr.toFixed(2)),
          activeAds: adsData.length,
          topPerformingAd
        },
        hourlyData,
        ads: adsData
      };
    },
    enabled: !!user && !!userBusinesses && userBusinesses.length > 0,
    refetchInterval: isLive ? 30000 : false, // Refresh every 30 seconds when live
  });

  useEffect(() => {
    if (realtimeData) {
      setLiveMetrics(realtimeData.metrics);
      setLiveData(realtimeData.hourlyData);
    }
  }, [realtimeData]);

  const toggleLiveMode = () => {
    setIsLive(!isLive);
    if (!isLive) {
      refetch();
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Activity className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600">Sign in to view real-time analytics</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-800 flex items-center gap-2">
            <Activity className="h-6 w-6 text-[#007749]" />
            Real-Time Analytics
            {isLive && (
              <Badge className="bg-green-100 text-green-800 animate-pulse">
                <Zap className="h-3 w-3 mr-1" />
                Live
              </Badge>
            )}
          </h2>
          <p className="text-slate-600">Monitor your ad performance in real-time</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isLive}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            variant={isLive ? "default" : "outline"}
            size="sm"
            onClick={toggleLiveMode}
            className={isLive ? "bg-green-600 hover:bg-green-700" : ""}
          >
            {isLive ? (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Live Mode On
              </>
            ) : (
              <>
                <Clock className="h-4 w-4 mr-2" />
                Enable Live Mode
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Live Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Eye className="h-5 w-5 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-slate-600">Impressions</span>
            </div>
            <div className="text-2xl font-bold text-slate-900">
              {liveMetrics.totalImpressions.toLocaleString()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <MousePointer className="h-5 w-5 text-green-600 mr-2" />
              <span className="text-sm font-medium text-slate-600">Clicks</span>
            </div>
            <div className="text-2xl font-bold text-slate-900">
              {liveMetrics.totalClicks.toLocaleString()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="h-5 w-5 text-purple-600 mr-2" />
              <span className="text-sm font-medium text-slate-600">Avg CTR</span>
            </div>
            <div className="text-2xl font-bold text-slate-900">
              {liveMetrics.avgCtr.toFixed(2)}%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Activity className="h-5 w-5 text-orange-600 mr-2" />
              <span className="text-sm font-medium text-slate-600">Active Ads</span>
            </div>
            <div className="text-2xl font-bold text-slate-900">
              {liveMetrics.activeAds}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <span className="text-sm font-medium text-slate-600">Top Performer</span>
            </div>
            <div className="text-sm font-bold text-slate-900 truncate">
              {liveMetrics.topPerformingAd}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Real-time Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-[#007749]" />
              Hourly Performance
            </CardTitle>
            <CardDescription>
              Impressions and clicks over the last 12 hours
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={liveData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="impressions" 
                  stroke="#007749" 
                  strokeWidth={2}
                  name="Impressions"
                />
                <Line 
                  type="monotone" 
                  dataKey="clicks" 
                  stroke="#FDB913" 
                  strokeWidth={2}
                  name="Clicks"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MousePointer className="h-5 w-5 text-[#007749]" />
              Click-Through Rate Trend
            </CardTitle>
            <CardDescription>
              CTR performance over the last 12 hours
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={liveData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}%`, 'CTR']} />
                <Bar dataKey="ctr" fill="#007749" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Live Activity Feed */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-[#007749]" />
            Live Activity Feed
          </CardTitle>
          <CardDescription>
            Recent ad interactions and performance updates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {isLive ? (
              <div className="text-center py-8">
                <div className="animate-pulse flex items-center justify-center gap-2 text-green-600">
                  <Zap className="h-5 w-5" />
                  <span>Monitoring live activity...</span>
                </div>
                <p className="text-sm text-slate-500 mt-2">
                  Real-time updates will appear here as they happen
                </p>
              </div>
            ) : (
              <div className="text-center py-8">
                <Clock className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-600">Enable live mode to see real-time activity</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RealTimeAnalytics;
